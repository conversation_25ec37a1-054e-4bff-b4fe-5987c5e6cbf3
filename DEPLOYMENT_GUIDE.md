# 🚀 Employee Billing System - Deployment Guide

## 📋 Table of Contents
1. [Prerequisites](#prerequisites)
2. [Local Development Setup](#local-development-setup)
3. [Production Deployment](#production-deployment)
4. [Environment Configuration](#environment-configuration)
5. [Database Setup](#database-setup)
6. [Security Configuration](#security-configuration)
7. [Performance Optimization](#performance-optimization)
8. [Monitoring & Maintenance](#monitoring--maintenance)
9. [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

### **System Requirements**
- **Node.js**: 18.0.0 or higher
- **PostgreSQL**: 14.0 or higher
- **npm**: 8.0.0 or higher
- **Git**: Latest version
- **Memory**: Minimum 4GB RAM
- **Storage**: Minimum 10GB free space

### **Development Tools**
- **Code Editor**: VS Code (recommended)
- **Database Client**: pgAdmin, DBeaver, or psql
- **API Testing**: Postman, Insomnia, or curl
- **Browser**: Chrome, Firefox, or Safari (latest versions)

## 💻 Local Development Setup

### **1. Clone Repository**
```bash
git clone <repository-url>
cd acsbilling
```

### **2. Backend Setup**
```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Configure environment variables (see Environment Configuration section)
nano .env

# Start development server
npm run dev
```

### **3. Frontend Setup**
```bash
# Navigate to frontend directory (in new terminal)
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

### **4. Database Setup**
```bash
# Create database
createdb acsbilling

# Run initialization scripts
psql -d acsbilling -f backend/src/database/init.sql
psql -d acsbilling -f backend/src/database/schema.sql

# Verify database setup
psql -d acsbilling -c "\dt"
```

### **5. Verify Installation**
- Backend: http://localhost:3000/api/health
- Frontend: http://localhost:5173
- Database: Check connection in backend logs

## 🌐 Production Deployment

### **1. Server Preparation**

#### **Ubuntu/Debian Server Setup**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib

# Install PM2 for process management
sudo npm install -g pm2

# Install Nginx for reverse proxy
sudo apt install nginx
```

#### **Create Application User**
```bash
sudo adduser acsbilling
sudo usermod -aG sudo acsbilling
su - acsbilling
```

### **2. Application Deployment**

#### **Deploy Backend**
```bash
# Clone repository
git clone <repository-url>
cd acsbilling/backend

# Install production dependencies
npm ci --only=production

# Create production environment file
cp .env.example .env
nano .env  # Configure production settings

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'acsbilling-backend',
    script: 'src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
EOF

# Start application with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### **Deploy Frontend**
```bash
cd ../frontend

# Install dependencies
npm ci

# Build for production
npm run build

# Copy build files to web server
sudo cp -r dist/* /var/www/acsbilling/
sudo chown -R www-data:www-data /var/www/acsbilling/
```

### **3. Nginx Configuration**
```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/acsbilling
```

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # Frontend
    location / {
        root /var/www/acsbilling;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # Backend API
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/acsbilling /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### **4. SSL Certificate (Let's Encrypt)**
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Verify auto-renewal
sudo certbot renew --dry-run
```

## ⚙️ Environment Configuration

### **Backend Environment Variables (.env)**
```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=acsbilling
DB_USER=acsbilling_user
DB_PASSWORD=secure_password

# Application Configuration
NODE_ENV=production
PORT=3000
API_BASE_URL=http://localhost:3000

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key
SESSION_SECRET=your-session-secret
BCRYPT_ROUNDS=12

# External Services
LOS_API_URL=https://los.allcheckservices.com
LOS_API_KEY=your-los-api-key

# Monitoring
LOG_LEVEL=info
HEALTH_CHECK_INTERVAL=30000

# Performance
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000
```

### **Frontend Environment Variables**
```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_NAME=Employee Billing System
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=false
```

## 🗄️ Database Setup

### **Production Database Configuration**

#### **1. Create Database User**
```sql
-- Connect as postgres user
sudo -u postgres psql

-- Create database and user
CREATE DATABASE acsbilling;
CREATE USER acsbilling_user WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE acsbilling TO acsbilling_user;

-- Grant schema permissions
\c acsbilling
GRANT ALL ON SCHEMA public TO acsbilling_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO acsbilling_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO acsbilling_user;

\q
```

#### **2. Initialize Database Schema**
```bash
# Run initialization scripts
psql -h localhost -U acsbilling_user -d acsbilling -f backend/src/database/init.sql
psql -h localhost -U acsbilling_user -d acsbilling -f backend/src/database/schema.sql
```

#### **3. Database Backup Strategy**
```bash
# Create backup script
cat > /home/<USER>/backup_db.sh << EOF
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

pg_dump -h localhost -U acsbilling_user acsbilling > $BACKUP_DIR/acsbilling_$DATE.sql
gzip $BACKUP_DIR/acsbilling_$DATE.sql

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
EOF

chmod +x /home/<USER>/backup_db.sh

# Add to crontab for daily backups
crontab -e
# Add: 0 2 * * * /home/<USER>/backup_db.sh
```

## 🔒 Security Configuration

### **1. Firewall Setup**
```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### **2. PostgreSQL Security**
```bash
# Edit PostgreSQL configuration
sudo nano /etc/postgresql/14/main/postgresql.conf

# Set listen_addresses = 'localhost'
# Set max_connections = 100

sudo nano /etc/postgresql/14/main/pg_hba.conf

# Ensure local connections use md5 authentication
# local   all             all                                     md5
# host    all             all             127.0.0.1/32            md5

sudo systemctl restart postgresql
```

### **3. Application Security**
```bash
# Set proper file permissions
sudo chown -R acsbilling:acsbilling /home/<USER>/acsbilling
chmod 600 /home/<USER>/acsbilling/backend/.env
chmod -R 755 /var/www/acsbilling
```

## ⚡ Performance Optimization

### **1. PM2 Configuration**
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'acsbilling-backend',
    script: 'src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
```

### **2. Nginx Optimization**
```nginx
# Add to nginx configuration
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# Enable caching
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### **3. Database Optimization**
```sql
-- Create indexes for better performance
CREATE INDEX idx_daily_counts_employee_date ON daily_counts(employee_id, date);
CREATE INDEX idx_daily_counts_status ON daily_counts(status);
CREATE INDEX idx_employees_email ON employees(email);
CREATE INDEX idx_employees_status ON employees(status);

-- Analyze tables
ANALYZE;
```

## 📊 Monitoring & Maintenance

### **1. PM2 Monitoring**
```bash
# Monitor processes
pm2 monit

# View logs
pm2 logs acsbilling-backend

# Restart application
pm2 restart acsbilling-backend

# Check status
pm2 status
```

### **2. System Monitoring**
```bash
# Install monitoring tools
sudo apt install htop iotop nethogs

# Monitor system resources
htop
iotop
nethogs

# Check disk usage
df -h
du -sh /home/<USER>/acsbilling
```

### **3. Log Management**
```bash
# Setup log rotation
sudo nano /etc/logrotate.d/acsbilling

# Add configuration:
/home/<USER>/acsbilling/backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 acsbilling acsbilling
    postrotate
        pm2 reloadLogs
    endscript
}
```

## 🔧 Troubleshooting

### **Common Issues**

#### **1. Database Connection Issues**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check database connectivity
psql -h localhost -U acsbilling_user -d acsbilling -c "SELECT 1;"

# Check logs
sudo tail -f /var/log/postgresql/postgresql-14-main.log
```

#### **2. Application Not Starting**
```bash
# Check PM2 logs
pm2 logs acsbilling-backend

# Check environment variables
pm2 env 0

# Restart application
pm2 restart acsbilling-backend
```

#### **3. Frontend Not Loading**
```bash
# Check Nginx status
sudo systemctl status nginx

# Check Nginx configuration
sudo nginx -t

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log
```

#### **4. Performance Issues**
```bash
# Check system resources
htop
free -h
df -h

# Check database performance
psql -d acsbilling -c "SELECT * FROM pg_stat_activity;"

# Check application metrics
pm2 monit
```

### **Health Check Endpoints**
- Backend Health: `GET /api/health`
- Database Status: Check backend logs
- Frontend Status: Access main URL

---

## 📞 Support

For deployment support:
- Check application logs: `pm2 logs`
- Review system logs: `/var/log/`
- Monitor system resources: `htop`, `free -h`
- Database logs: `/var/log/postgresql/`

**Deployment Version**: 1.0.0
**Last Updated**: May 29, 2025
