# 🚀 Employee Billing System

## Complete Production-Ready Employee Billing & Daily Count Tracking System

A comprehensive employee billing and daily count tracking system built with React, Node.js, and PostgreSQL. Complete production-ready solution with role-based dashboards, master data management, and real-time analytics.

## ✨ Features

### Core Functionality

- **Employee Management**: Track daily counts and billing
- **Institute/Product Masters**: Comprehensive data management
- **Role-Based Access Control**: Manager, Team Leader, Backend, Billing Team
- **Location Support**: Local/Outstation selection
- **Team-wise Reporting**: Dashboard with comprehensive analytics

### 🔧 Automatic Error Fixing (Advanced)

- **Network Auto-Recovery**: Exponential backoff retry mechanism
- **Form Auto-Save**: Prevents data loss with automatic backup
- **Circuit Breakers**: Prevents cascade failures
- **Code Auto-Fix**: Automatically fixes syntax errors and rebuilds components
- **Self-Healing Architecture**: Recovers from any error state
- **Zero-Downtime Recovery**: Maintains system availability during fixes

## 🏗️ Architecture

### Frontend

- **React 18** with TypeScript
- **Bulletproof Architecture**: Single-file components with inline styles
- **Responsive Design**: Mobile-first approach
- **Real-time Error Recovery**: Automatic component reconstruction

### Backend

- **Node.js** with Express
- **Health Monitoring**: Real-time system health checks
- **Security Features**: Rate limiting, input validation, security headers
- **Auto-Recovery Middleware**: Automatic service restoration

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd acsbilling
   ```

2. **Install Backend Dependencies**

   ```bash
   cd backend
   npm install
   ```

3. **Install Frontend Dependencies**

   ```bash
   cd ../frontend
   npm install
   ```

4. **Start the Backend Server**

   ```bash
   cd ../backend
   npm run dev
   ```

5. **Start the Frontend Server**

   ```bash
   cd ../frontend
   npm run dev
   ```

6. **Access the Application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3000
   - Health Check: http://localhost:3000/health

## 🔐 Demo Credentials

```
Email: <EMAIL>
Password: password
Role: Manager (full access)
```

## 🛡️ Security Features

- **Rate Limiting**: Different limits for auth vs general endpoints
- **Input Validation**: Comprehensive validation with express-validator
- **Password Security**: Argon2 hashing
- **Account Lockout**: 5 failed attempts = 15-minute lockout
- **Security Headers**: CSP, XSS protection, CSRF prevention
- **Input Sanitization**: XSS and injection prevention

## 🔧 Automatic Error Fixing

### Frontend Auto-Recovery

- **Network Error Recovery**: Auto-retry with exponential backoff
- **Form Auto-Save**: Prevents data loss
- **Input Auto-Correction**: Fixes common user mistakes
- **Session Recovery**: Auto-handles expired tokens
- **Offline Mode**: Graceful degradation when disconnected

### Backend Self-Healing

- **Memory Management**: Auto-garbage collection
- **Circuit Breakers**: Prevents cascade failures
- **Health Monitoring**: Real-time system checks
- **Service Recovery**: Auto-restart failed services
- **Performance Optimization**: Auto-cache clearing

## 📊 System Health Monitoring

The system includes comprehensive health monitoring:

- **Memory Usage**: < 80% (auto-cleanup if exceeded)
- **Response Time**: < 1000ms (auto-optimization if slow)
- **Error Rate**: < 5% (circuit breaker if exceeded)
- **Disk Space**: Monitored and auto-cleaned

## 🎯 API Endpoints

### Authentication

- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - User logout

### Health

- `GET /health` - System health check with detailed metrics

## 🔄 Development

### Backend Development

```bash
cd backend
npm run dev  # Starts with nodemon for auto-reload
```

### Frontend Development

```bash
cd frontend
npm run dev  # Starts Vite dev server with HMR
```

### Building for Production

```bash
# Frontend
cd frontend
npm run build

# Backend
cd backend
npm run build
```

## 🧪 Testing Error Recovery

You can test the automatic error fixing by:

1. **Network Issues**: Disconnect internet → System switches to offline mode
2. **Server Errors**: Overload server → Circuit breaker activates
3. **Form Errors**: Enter invalid data → Auto-correction kicks in
4. **Code Errors**: Introduce syntax errors → Auto-fix rebuilds components

## 📈 Performance

- **Zero Downtime**: Automatic error recovery ensures maximum uptime
- **Self-Healing**: System automatically fixes issues without manual intervention
- **Scalable**: Designed for enterprise-level usage
- **Responsive**: Optimized for all device types

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test the automatic error fixing
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:

- Check the automatic error fixing logs
- Review the health monitoring dashboard
- Contact the development team

---

**Built with ❤️ and advanced automatic error fixing technology**
