import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Plus, Edit, Trash2, Save, X } from 'lucide-react';

type MasterType = 'institutes' | 'products' | 'services' | 'teams';

interface MasterItem {
  id: string;
  name: string;
  description?: string;
  address?: string;
  billing_format?: string;
  base_rate?: number;
  team_leader_id?: string;
}

const Masters: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<MasterType>('institutes');
  const [showForm, setShowForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<MasterItem>>({});

  // Mock data
  const [data, setData] = useState({
    institutes: [
      { id: '1', name: 'Institute A', address: '123 Main St', billing_format: 'Standard Format' },
      { id: '2', name: 'Institute B', address: '456 Oak Ave', billing_format: 'Premium Format' }
    ],
    products: [
      { id: '1', name: 'Product A', description: 'Description A', base_rate: 100 },
      { id: '2', name: 'Product B', description: 'Description B', base_rate: 150 }
    ],
    services: [
      { id: '1', name: 'Service A', description: 'Service A Description' },
      { id: '2', name: 'Service B', description: 'Service B Description' }
    ],
    teams: [
      { id: '1', name: 'Development Team', team_leader_id: '1' },
      { id: '2', name: 'QA Team', team_leader_id: '2' }
    ]
  });

  const tabs = [
    { id: 'institutes', name: 'Institutes', roles: ['manager', 'billing_team'] },
    { id: 'products', name: 'Products', roles: ['manager', 'billing_team'] },
    { id: 'services', name: 'Services', roles: ['manager', 'billing_team'] },
    { id: 'teams', name: 'Teams', roles: ['manager'] }
  ];

  const filteredTabs = tabs.filter(tab => 
    tab.roles.includes(user?.role || '')
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingId) {
      // Update existing item
      setData(prev => ({
        ...prev,
        [activeTab]: prev[activeTab].map(item => 
          item.id === editingId 
            ? { ...item, ...formData }
            : item
        )
      }));
      setEditingId(null);
    } else {
      // Add new item
      const newItem = {
        id: Date.now().toString(),
        ...formData
      } as MasterItem;
      
      setData(prev => ({
        ...prev,
        [activeTab]: [...prev[activeTab], newItem]
      }));
    }

    setFormData({});
    setShowForm(false);
  };

  const handleEdit = (item: MasterItem) => {
    setFormData(item);
    setEditingId(item.id);
    setShowForm(true);
  };

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this item?')) {
      setData(prev => ({
        ...prev,
        [activeTab]: prev[activeTab].filter(item => item.id !== id)
      }));
    }
  };

  const renderForm = () => {
    switch (activeTab) {
      case 'institutes':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700">Name</label>
              <input
                type="text"
                value={formData.name || ''}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Address</label>
              <textarea
                value={formData.address || ''}
                onChange={(e) => setFormData({...formData, address: e.target.value})}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                rows={3}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Billing Format</label>
              <input
                type="text"
                value={formData.billing_format || ''}
                onChange={(e) => setFormData({...formData, billing_format: e.target.value})}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              />
            </div>
          </>
        );
      
      case 'products':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700">Name</label>
              <input
                type="text"
                value={formData.name || ''}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                rows={3}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Base Rate</label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.base_rate || ''}
                onChange={(e) => setFormData({...formData, base_rate: parseFloat(e.target.value)})}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              />
            </div>
          </>
        );
      
      case 'services':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700">Name</label>
              <input
                type="text"
                value={formData.name || ''}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                rows={3}
                required
              />
            </div>
          </>
        );
      
      case 'teams':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700">Team Name</label>
              <input
                type="text"
                value={formData.name || ''}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Team Leader ID</label>
              <input
                type="text"
                value={formData.team_leader_id || ''}
                onChange={(e) => setFormData({...formData, team_leader_id: e.target.value})}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              />
            </div>
          </>
        );
      
      default:
        return null;
    }
  };

  const renderTable = () => {
    const items = data[activeTab];
    
    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              {activeTab === 'institutes' && (
                <>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Address
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Billing Format
                  </th>
                </>
              )}
              {(activeTab === 'products' || activeTab === 'services') && (
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
              )}
              {activeTab === 'products' && (
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Base Rate
                </th>
              )}
              {activeTab === 'teams' && (
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Team Leader ID
                </th>
              )}
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {items.map((item) => (
              <tr key={item.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {item.name}
                </td>
                {activeTab === 'institutes' && (
                  <>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {item.address}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.billing_format}
                    </td>
                  </>
                )}
                {(activeTab === 'products' || activeTab === 'services') && (
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {item.description}
                  </td>
                )}
                {activeTab === 'products' && (
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${item.base_rate}
                  </td>
                )}
                {activeTab === 'teams' && (
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.team_leader_id}
                  </td>
                )}
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEdit(item)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Master Data Management</h1>
          <p className="text-gray-600">Manage institutes, products, services, and teams</p>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add {activeTab.slice(0, -1)}
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {filteredTabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as MasterType)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">
                {editingId ? 'Edit' : 'Add'} {activeTab.slice(0, -1)}
              </h3>
              <button onClick={() => setShowForm(false)}>
                <X className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {renderForm()}
              
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {editingId ? 'Update' : 'Save'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 capitalize">
            {activeTab}
          </h3>
        </div>
        {renderTable()}
      </div>
    </div>
  );
};

export default Masters;
