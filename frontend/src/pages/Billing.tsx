import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { FileText, Download, Calendar, DollarSign } from 'lucide-react';

interface BillItem {
  product: string;
  service: string;
  location_type: string;
  quantity: number;
  rate: number;
  amount: number;
}

interface Bill {
  bill_id: string;
  institute_id: string;
  institute_name: string;
  period: string;
  items: BillItem[];
  subtotal: number;
  tax: number;
  total: number;
  generated_at: string;
  generated_by: string;
}

const Billing: React.FC = () => {
  const { user } = useAuth();
  const [selectedInstitute, setSelectedInstitute] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [generatedBill, setGeneratedBill] = useState<Bill | null>(null);
  const [loading, setLoading] = useState(false);

  const institutes = [
    { id: '1', name: 'Institute A' },
    { id: '2', name: 'Institute B' }
  ];

  const [billingFormats, setBillingFormats] = useState([
    {
      id: '1',
      institute_id: '1',
      format_template: 'Standard billing format for {institute_name}',
      rates: {
        local: 100,
        ogl: 120,
        outstation: 150
      }
    }
  ]);

  const handleGenerateBill = async () => {
    if (!selectedInstitute || !startDate || !endDate) {
      alert('Please fill in all required fields');
      return;
    }

    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const mockBill: Bill = {
        bill_id: `BILL-${Date.now()}`,
        institute_id: selectedInstitute,
        institute_name: institutes.find(i => i.id === selectedInstitute)?.name || 'Unknown',
        period: `${startDate} to ${endDate}`,
        items: [
          {
            product: 'Product A',
            service: 'Service A',
            location_type: 'local',
            quantity: 10,
            rate: 100,
            amount: 1000
          },
          {
            product: 'Product B',
            service: 'Service B',
            location_type: 'ogl',
            quantity: 5,
            rate: 120,
            amount: 600
          },
          {
            product: 'Product A',
            service: 'Service A',
            location_type: 'outstation',
            quantity: 3,
            rate: 150,
            amount: 450
          }
        ],
        subtotal: 2050,
        tax: 205,
        total: 2255,
        generated_at: new Date().toISOString(),
        generated_by: user?.name || 'Unknown'
      };
      
      setGeneratedBill(mockBill);
      setLoading(false);
    }, 2000);
  };

  const handleDownloadBill = () => {
    if (!generatedBill) return;
    
    // Create a simple text representation of the bill
    const billText = `
BILL INVOICE
============

Bill ID: ${generatedBill.bill_id}
Institute: ${generatedBill.institute_name}
Period: ${generatedBill.period}
Generated: ${new Date(generatedBill.generated_at).toLocaleString()}
Generated By: ${generatedBill.generated_by}

ITEMS:
------
${generatedBill.items.map(item => 
  `${item.product} - ${item.service} (${item.location_type.toUpperCase()})
  Quantity: ${item.quantity} x $${item.rate} = $${item.amount}`
).join('\n')}

SUMMARY:
--------
Subtotal: $${generatedBill.subtotal}
Tax (10%): $${generatedBill.tax}
Total: $${generatedBill.total}
    `;

    const blob = new Blob([billText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${generatedBill.bill_id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (user?.role !== 'billing_team' && user?.role !== 'manager') {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900">Access Denied</h2>
        <p className="text-gray-600 mt-2">You don't have permission to access billing features.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Billing Management</h1>
        <p className="text-gray-600">Generate bills and manage billing formats</p>
      </div>

      {/* Bill Generation Form */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Generate Bill</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Institute</label>
            <select
              value={selectedInstitute}
              onChange={(e) => setSelectedInstitute(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">Select Institute</option>
              {institutes.map(institute => (
                <option key={institute.id} value={institute.id}>{institute.name}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Start Date</label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">End Date</label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
        </div>

        <div className="mt-4">
          <button
            onClick={handleGenerateBill}
            disabled={loading}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
            ) : (
              <FileText className="h-5 w-5 mr-2" />
            )}
            {loading ? 'Generating...' : 'Generate Bill'}
          </button>
        </div>
      </div>

      {/* Generated Bill */}
      {generatedBill && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-gray-900">Generated Bill</h3>
            <button
              onClick={handleDownloadBill}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <Download className="h-5 w-5 mr-2" />
              Download
            </button>
          </div>

          <div className="border border-gray-200 rounded-lg p-6">
            {/* Bill Header */}
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">INVOICE</h2>
              <p className="text-gray-600">Bill ID: {generatedBill.bill_id}</p>
            </div>

            {/* Bill Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h4 className="font-medium text-gray-900">Bill To:</h4>
                <p className="text-gray-600">{generatedBill.institute_name}</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Period:</h4>
                <p className="text-gray-600">{generatedBill.period}</p>
                <h4 className="font-medium text-gray-900 mt-2">Generated:</h4>
                <p className="text-gray-600">{new Date(generatedBill.generated_at).toLocaleString()}</p>
              </div>
            </div>

            {/* Bill Items */}
            <div className="mb-6">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product/Service
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {generatedBill.items.map((item, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.product} - {item.service}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          item.location_type === 'local' ? 'bg-green-100 text-green-800' :
                          item.location_type === 'ogl' ? 'bg-blue-100 text-blue-800' :
                          'bg-orange-100 text-orange-800'
                        }`}>
                          {item.location_type.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.quantity}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${item.rate}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${item.amount}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Bill Summary */}
            <div className="border-t border-gray-200 pt-4">
              <div className="flex justify-end">
                <div className="w-64">
                  <div className="flex justify-between py-2">
                    <span className="text-gray-600">Subtotal:</span>
                    <span className="font-medium">${generatedBill.subtotal}</span>
                  </div>
                  <div className="flex justify-between py-2">
                    <span className="text-gray-600">Tax (10%):</span>
                    <span className="font-medium">${generatedBill.tax}</span>
                  </div>
                  <div className="flex justify-between py-2 border-t border-gray-200">
                    <span className="text-lg font-bold">Total:</span>
                    <span className="text-lg font-bold text-blue-600">${generatedBill.total}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Billing Formats */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Billing Formats</h3>
        
        <div className="space-y-4">
          {billingFormats.map((format) => (
            <div key={format.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-medium text-gray-900">
                    Institute: {institutes.find(i => i.id === format.institute_id)?.name}
                  </h4>
                  <p className="text-gray-600 mt-1">{format.format_template}</p>
                  <div className="mt-2">
                    <span className="text-sm font-medium text-gray-700">Rates:</span>
                    <div className="flex space-x-4 mt-1">
                      <span className="text-sm text-gray-600">Local: ${format.rates.local}</span>
                      <span className="text-sm text-gray-600">OGL: ${format.rates.ogl}</span>
                      <span className="text-sm text-gray-600">Outstation: ${format.rates.outstation}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Billing;
