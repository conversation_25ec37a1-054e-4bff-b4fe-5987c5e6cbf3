import React, { useState, useEffect } from "react";
import { useAuth } from "../contexts/AuthContext";
import { Link } from "react-router-dom";

interface DashboardStats {
  totalCounts: number;
  todayCounts: number;
  instituteCounts: Array<{
    institute_id: string;
    institute_name: string;
    count: number;
  }>;
  productCounts: Array<{
    product_id: string;
    product_name: string;
    count: number;
  }>;
  instituteProductCounts: Array<{
    key: string;
    institute_id: string;
    institute_name: string;
    product_id: string;
    product_name: string;
    count: number;
    services: Array<{
      service_id: string;
      service_name: string;
      count: number;
      location_type: string;
      verifier_name: string;
    }>;
  }>;
  teamCounts: Array<{
    team_name: string;
    count: number;
  }>;
  locationCounts: {
    local: number;
    ogl: number;
    outstation: number;
  };
}

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch("/api/dashboard/stats", {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <div>Loading dashboard...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: "2rem", maxWidth: "1200px", margin: "0 auto" }}>
      {/* Header */}
      <div style={{ marginBottom: "2rem" }}>
        <h1
          style={{
            fontSize: "2rem",
            fontWeight: "bold",
            marginBottom: "0.5rem",
          }}
        >
          Dashboard
        </h1>
        <p style={{ color: "var(--muted-foreground)" }}>
          Welcome back, {user?.name}! Here's your overview.
        </p>
      </div>

      {/* Quick Add Daily Count Button */}
      <div style={{ marginBottom: "2rem" }}>
        <Link
          to="/daily-count"
          style={{
            display: "inline-flex",
            alignItems: "center",
            padding: "0.75rem 1.5rem",
            backgroundColor: "#3b82f6",
            color: "white",
            textDecoration: "none",
            borderRadius: "0.5rem",
            fontWeight: "500",
            fontSize: "1rem",
          }}
        >
          ➕ Add Daily Count
        </Link>
      </div>

      {/* Stats Grid */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
          gap: "1.5rem",
          marginBottom: "2rem",
        }}
      >
        <div className="card p-6">
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <div>
              <p
                style={{
                  fontSize: "0.875rem",
                  fontWeight: "500",
                  color: "var(--muted-foreground)",
                }}
              >
                Total Counts
              </p>
              <p style={{ fontSize: "1.5rem", fontWeight: "bold" }}>
                {stats?.totalCounts || 0}
              </p>
            </div>
            <div
              style={{
                padding: "0.75rem",
                backgroundColor: "rgba(59, 130, 246, 0.1)",
                borderRadius: "50%",
              }}
            >
              📊
            </div>
          </div>
          <div
            style={{ marginTop: "1rem", display: "flex", alignItems: "center" }}
          >
            <span style={{ color: "#10b981", marginRight: "0.25rem" }}>📈</span>
            <span style={{ fontSize: "0.875rem", color: "#10b981" }}>
              All time total
            </span>
          </div>
        </div>

        <div className="card p-6">
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <div>
              <p
                style={{
                  fontSize: "0.875rem",
                  fontWeight: "500",
                  color: "var(--muted-foreground)",
                }}
              >
                Today's Counts
              </p>
              <p style={{ fontSize: "1.5rem", fontWeight: "bold" }}>
                {stats?.todayCounts || 0}
              </p>
            </div>
            <div
              style={{
                padding: "0.75rem",
                backgroundColor: "rgba(16, 185, 129, 0.1)",
                borderRadius: "50%",
              }}
            >
              📅
            </div>
          </div>
          <div
            style={{ marginTop: "1rem", display: "flex", alignItems: "center" }}
          >
            <span style={{ color: "#10b981", marginRight: "0.25rem" }}>📈</span>
            <span style={{ fontSize: "0.875rem", color: "#10b981" }}>
              Today's entries
            </span>
          </div>
        </div>

        <div className="card p-6">
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <div>
              <p
                style={{
                  fontSize: "0.875rem",
                  fontWeight: "500",
                  color: "var(--muted-foreground)",
                }}
              >
                Institutes
              </p>
              <p style={{ fontSize: "1.5rem", fontWeight: "bold" }}>
                {stats?.instituteCounts?.length || 0}
              </p>
            </div>
            <div
              style={{
                padding: "0.75rem",
                backgroundColor: "rgba(139, 92, 246, 0.1)",
                borderRadius: "50%",
              }}
            >
              🏢
            </div>
          </div>
          <div style={{ marginTop: "1rem" }}>
            <span
              style={{ fontSize: "0.875rem", color: "var(--muted-foreground)" }}
            >
              Assigned institutes
            </span>
          </div>
        </div>

        <div className="card p-6">
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <div>
              <p
                style={{
                  fontSize: "0.875rem",
                  fontWeight: "500",
                  color: "var(--muted-foreground)",
                }}
              >
                Products
              </p>
              <p style={{ fontSize: "1.5rem", fontWeight: "bold" }}>
                {stats?.productCounts?.length || 0}
              </p>
            </div>
            <div
              style={{
                padding: "0.75rem",
                backgroundColor: "rgba(245, 158, 11, 0.1)",
                borderRadius: "50%",
              }}
            >
              📦
            </div>
          </div>
          <div
            style={{ marginTop: "1rem", display: "flex", alignItems: "center" }}
          >
            <span style={{ color: "#10b981", marginRight: "0.25rem" }}>📈</span>
            <span style={{ fontSize: "0.875rem", color: "#10b981" }}>
              Assigned products
            </span>
          </div>
        </div>
      </div>

      {/* Institute & Product-wise Daily Counts */}
      <div className="card p-6" style={{ marginBottom: "2rem" }}>
        <h3
          style={{
            fontSize: "1.125rem",
            fontWeight: "600",
            marginBottom: "1rem",
          }}
        >
          📊 Daily Counts by Institute & Product
        </h3>

        {stats?.instituteProductCounts &&
        stats.instituteProductCounts.length > 0 ? (
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(350px, 1fr))",
              gap: "1rem",
            }}
          >
            {stats.instituteProductCounts.map((item) => (
              <div
                key={item.key}
                style={{
                  padding: "1rem",
                  backgroundColor: "rgba(59, 130, 246, 0.05)",
                  borderRadius: "calc(var(--radius) - 2px)",
                  border: "1px solid rgba(59, 130, 246, 0.1)",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    marginBottom: "0.5rem",
                  }}
                >
                  <div>
                    <span style={{ fontWeight: "600", fontSize: "0.9rem" }}>
                      {item.institute_name}
                    </span>
                    <span
                      style={{
                        margin: "0 0.5rem",
                        color: "var(--muted-foreground)",
                      }}
                    >
                      •
                    </span>
                    <span style={{ fontWeight: "500", fontSize: "0.9rem" }}>
                      {item.product_name}
                    </span>
                  </div>
                  <span
                    style={{
                      fontWeight: "bold",
                      fontSize: "1.1rem",
                      color: "#3b82f6",
                    }}
                  >
                    {item.count}
                  </span>
                </div>

                {item.services && item.services.length > 0 && (
                  <div style={{ marginTop: "0.75rem" }}>
                    <p
                      style={{
                        fontSize: "0.8rem",
                        color: "var(--muted-foreground)",
                        marginBottom: "0.5rem",
                      }}
                    >
                      Services:
                    </p>
                    {item.services.map((service, index) => (
                      <div
                        key={index}
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          fontSize: "0.8rem",
                          marginBottom: "0.25rem",
                          padding: "0.25rem 0.5rem",
                          backgroundColor: "rgba(255, 255, 255, 0.5)",
                          borderRadius: "0.25rem",
                        }}
                      >
                        <span>{service.service_name}</span>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "0.5rem",
                          }}
                        >
                          <span style={{ fontWeight: "500" }}>
                            {service.count}
                          </span>
                          <span
                            style={{
                              fontSize: "0.7rem",
                              padding: "0.1rem 0.3rem",
                              backgroundColor:
                                service.location_type === "local"
                                  ? "#10b981"
                                  : service.location_type === "ogl"
                                  ? "#f59e0b"
                                  : "#ef4444",
                              color: "white",
                              borderRadius: "0.2rem",
                            }}
                          >
                            {service.location_type}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div
            style={{
              textAlign: "center",
              padding: "2rem",
              color: "var(--muted-foreground)",
            }}
          >
            <p>No daily counts recorded yet.</p>
            <Link
              to="/daily-count"
              style={{
                display: "inline-flex",
                alignItems: "center",
                marginTop: "1rem",
                padding: "0.5rem 1rem",
                backgroundColor: "#3b82f6",
                color: "white",
                textDecoration: "none",
                borderRadius: "0.375rem",
                fontSize: "0.875rem",
              }}
            >
              Add Your First Count
            </Link>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="card p-6">
        <h3
          style={{
            fontSize: "1.125rem",
            fontWeight: "600",
            marginBottom: "1rem",
          }}
        >
          Quick Actions
        </h3>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(150px, 1fr))",
            gap: "1rem",
          }}
        >
          <Link
            to="/daily-count"
            className="btn p-4 border rounded-lg"
            style={{
              transition: "background-color 0.2s",
              cursor: "pointer",
              textDecoration: "none",
              color: "inherit",
            }}
          >
            <div style={{ textAlign: "center" }}>
              <div style={{ fontSize: "1.5rem", marginBottom: "0.5rem" }}>
                📅
              </div>
              <span style={{ fontSize: "0.875rem", fontWeight: "500" }}>
                Add Daily Count
              </span>
            </div>
          </Link>

          <Link
            to="/daily-count"
            className="btn p-4 border rounded-lg"
            style={{
              transition: "background-color 0.2s",
              cursor: "pointer",
              textDecoration: "none",
              color: "inherit",
            }}
          >
            <div style={{ textAlign: "center" }}>
              <div style={{ fontSize: "1.5rem", marginBottom: "0.5rem" }}>
                📊
              </div>
              <span style={{ fontSize: "0.875rem", fontWeight: "500" }}>
                View All Counts
              </span>
            </div>
          </Link>

          {(user?.role === "manager" || user?.role === "billing_team") && (
            <Link
              to="/billing"
              className="btn p-4 border rounded-lg"
              style={{
                transition: "background-color 0.2s",
                cursor: "pointer",
                textDecoration: "none",
                color: "inherit",
              }}
            >
              <div style={{ textAlign: "center" }}>
                <div style={{ fontSize: "1.5rem", marginBottom: "0.5rem" }}>
                  📄
                </div>
                <span style={{ fontSize: "0.875rem", fontWeight: "500" }}>
                  Generate Bill
                </span>
              </div>
            </Link>
          )}

          {(user?.role === "manager" || user?.role === "billing_team") && (
            <Link
              to="/masters"
              className="btn p-4 border rounded-lg"
              style={{
                transition: "background-color 0.2s",
                cursor: "pointer",
                textDecoration: "none",
                color: "inherit",
              }}
            >
              <div style={{ textAlign: "center" }}>
                <div style={{ fontSize: "1.5rem", marginBottom: "0.5rem" }}>
                  ⚙️
                </div>
                <span style={{ fontSize: "0.875rem", fontWeight: "500" }}>
                  Manage Masters
                </span>
              </div>
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
