import { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from '../utils/errorHandler';
import { toast } from './useToast';

interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
  autoFix?: (value: any) => any;
}

interface FieldConfig {
  [key: string]: ValidationRule;
}

interface FormState<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  isValid: boolean;
  isSubmitting: boolean;
}

interface AutoFormOptions<T> {
  initialValues: T;
  validationRules: FieldConfig;
  onSubmit: (values: T) => Promise<void>;
  autoSave?: boolean;
  autoSaveDelay?: number;
  enableAutoFix?: boolean;
}

export function useAutoForm<T extends Record<string, any>>({
  initialValues,
  validationRules,
  onSubmit,
  autoSave = false,
  autoSaveDelay = 2000,
  enableAutoFix = true,
}: AutoFormOptions<T>) {
  const [state, setState] = useState<FormState<T>>({
    values: { ...initialValues },
    errors: {},
    touched: {},
    isValid: false,
    isSubmitting: false,
  });

  // Auto-save timer
  const [autoSaveTimer, setAutoSaveTimer] = useState<NodeJS.Timeout | null>(null);

  // Validation function with auto-fix
  const validateField = useCallback((name: keyof T, value: any): string | null => {
    const rule = validationRules[name as string];
    if (!rule) return null;

    // Auto-fix value if enabled
    if (enableAutoFix && rule.autoFix) {
      const fixedValue = rule.autoFix(value);
      if (fixedValue !== value) {
        setState(prev => ({
          ...prev,
          values: { ...prev.values, [name]: fixedValue }
        }));
        
        toast({
          title: "Auto-corrected",
          description: `${String(name)} has been automatically corrected.`,
          variant: "success"
        });
        
        value = fixedValue;
      }
    }

    // Required validation
    if (rule.required && (!value || value.toString().trim() === '')) {
      return `${String(name)} is required`;
    }

    // Length validations
    if (value && typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        return `${String(name)} must be at least ${rule.minLength} characters`;
      }
      if (rule.maxLength && value.length > rule.maxLength) {
        return `${String(name)} must be no more than ${rule.maxLength} characters`;
      }
    }

    // Pattern validation
    if (rule.pattern && value && !rule.pattern.test(value.toString())) {
      return `${String(name)} format is invalid`;
    }

    // Custom validation
    if (rule.custom) {
      return rule.custom(value);
    }

    return null;
  }, [validationRules, enableAutoFix]);

  // Validate all fields
  const validateForm = useCallback(() => {
    const errors: Partial<Record<keyof T, string>> = {};
    let isValid = true;

    Object.keys(validationRules).forEach(key => {
      const error = validateField(key as keyof T, state.values[key as keyof T]);
      if (error) {
        errors[key as keyof T] = error;
        isValid = false;
      }
    });

    setState(prev => ({ ...prev, errors, isValid }));
    return isValid;
  }, [state.values, validateField, validationRules]);

  // Handle field change with auto-validation and auto-fix
  const handleChange = useCallback((name: keyof T, value: any) => {
    try {
      setState(prev => {
        const newValues = { ...prev.values, [name]: value };
        const error = validateField(name, value);
        const newErrors = { ...prev.errors };
        
        if (error) {
          newErrors[name] = error;
        } else {
          delete newErrors[name];
        }

        const isValid = Object.keys(newErrors).length === 0;

        return {
          ...prev,
          values: newValues,
          errors: newErrors,
          touched: { ...prev.touched, [name]: true },
          isValid,
        };
      });

      // Auto-save functionality
      if (autoSave) {
        if (autoSaveTimer) {
          clearTimeout(autoSaveTimer);
        }
        
        const timer = setTimeout(() => {
          handleAutoSave();
        }, autoSaveDelay);
        
        setAutoSaveTimer(timer);
      }

    } catch (error) {
      ErrorHandler.handleError(error, `form_field_${String(name)}`);
    }
  }, [validateField, autoSave, autoSaveDelay, autoSaveTimer]);

  // Auto-save handler
  const handleAutoSave = useCallback(async () => {
    try {
      if (state.isValid) {
        // Save to localStorage as backup
        localStorage.setItem(`form_backup_${Date.now()}`, JSON.stringify(state.values));
        
        toast({
          title: "Auto-saved",
          description: "Your changes have been automatically saved.",
          variant: "success"
        });
      }
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  }, [state.values, state.isValid]);

  // Handle form submission with automatic error recovery
  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    setState(prev => ({ ...prev, isSubmitting: true }));

    try {
      // Final validation
      const isValid = validateForm();
      if (!isValid) {
        toast({
          title: "Validation Error",
          description: "Please fix the errors before submitting.",
          variant: "destructive"
        });
        return;
      }

      // Attempt submission with retry logic
      await onSubmit(state.values);
      
      // Clear form backup on successful submission
      const backupKeys = Object.keys(localStorage).filter(key => key.startsWith('form_backup_'));
      backupKeys.forEach(key => localStorage.removeItem(key));
      
      toast({
        title: "Success",
        description: "Form submitted successfully!",
        variant: "success"
      });

    } catch (error: any) {
      await ErrorHandler.handleError(error, 'form_submission');
      
      // Auto-recovery: save current state for later retry
      localStorage.setItem('failed_submission', JSON.stringify({
        values: state.values,
        timestamp: Date.now(),
        error: error.message
      }));
      
      toast({
        title: "Submission Failed",
        description: "Your data has been saved. You can retry later.",
        variant: "destructive"
      });
      
    } finally {
      setState(prev => ({ ...prev, isSubmitting: false }));
    }
  }, [state.values, validateForm, onSubmit]);

  // Auto-recovery: restore failed submission
  const restoreFailedSubmission = useCallback(() => {
    try {
      const failedSubmission = localStorage.getItem('failed_submission');
      if (failedSubmission) {
        const { values, timestamp } = JSON.parse(failedSubmission);
        
        // Only restore if it's recent (within 1 hour)
        if (Date.now() - timestamp < 3600000) {
          setState(prev => ({ ...prev, values }));
          localStorage.removeItem('failed_submission');
          
          toast({
            title: "Form Restored",
            description: "Your previous unsaved changes have been restored.",
            variant: "success"
          });
        }
      }
    } catch (error) {
      console.error('Failed to restore submission:', error);
    }
  }, []);

  // Reset form with auto-backup
  const reset = useCallback(() => {
    // Backup current state before reset
    if (Object.keys(state.touched).length > 0) {
      localStorage.setItem('form_reset_backup', JSON.stringify(state.values));
    }
    
    setState({
      values: { ...initialValues },
      errors: {},
      touched: {},
      isValid: false,
      isSubmitting: false,
    });
  }, [initialValues, state.values, state.touched]);

  // Auto-fix common issues
  const autoFixCommonIssues = useCallback(() => {
    const fixedValues = { ...state.values };
    let hasChanges = false;

    Object.keys(validationRules).forEach(key => {
      const rule = validationRules[key];
      const value = fixedValues[key as keyof T];

      if (rule.autoFix && value !== undefined) {
        const fixedValue = rule.autoFix(value);
        if (fixedValue !== value) {
          fixedValues[key as keyof T] = fixedValue;
          hasChanges = true;
        }
      }
    });

    if (hasChanges) {
      setState(prev => ({ ...prev, values: fixedValues }));
      validateForm();
      
      toast({
        title: "Auto-fixed Issues",
        description: "Common form issues have been automatically corrected.",
        variant: "success"
      });
    }
  }, [state.values, validationRules, validateForm]);

  // Initialize form with recovery check
  useEffect(() => {
    restoreFailedSubmission();
  }, [restoreFailedSubmission]);

  // Cleanup auto-save timer
  useEffect(() => {
    return () => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }
    };
  }, [autoSaveTimer]);

  return {
    values: state.values,
    errors: state.errors,
    touched: state.touched,
    isValid: state.isValid,
    isSubmitting: state.isSubmitting,
    handleChange,
    handleSubmit,
    reset,
    validateForm,
    autoFixCommonIssues,
    restoreFailedSubmission,
  };
}
