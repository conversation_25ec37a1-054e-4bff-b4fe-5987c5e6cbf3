export interface User {
  id: string;
  email: string;
  name: string;
  role: "employee" | "team_leader" | "manager" | "billing_team";
  team_id?: string;
  manager_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Team {
  id: string;
  name: string;
  team_leader_id: string;
  created_at: string;
  updated_at: string;
}

export interface Institute {
  id: string;
  name: string;
  address: string;
  billing_format: string;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  base_rate: number;
  created_at: string;
  updated_at: string;
}

export interface Service {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface DailyCount {
  id: string;
  user_id: string;
  institute_id: string;
  product_id: string;
  service_id: string;
  count: number;
  location_type: "local" | "ogl" | "outstation";
  verifier_name: string;
  date: string;
  created_at: string;
  updated_at: string;
}

export interface DailyCountEntry {
  id: string;
  institute_id: string;
  product_id: string;
  service_id: string;
  count: number;
  location_type: "local" | "ogl" | "outstation";
  verifier_name: string;
  date: string;
}

export interface BillingFormat {
  id: string;
  institute_id: string;
  format_template: string;
  rates: Record<string, number>;
  created_at: string;
  updated_at: string;
}

export interface DashboardStats {
  totalCounts: number;
  todayCounts: number;
  teamCounts: Array<{
    team_name: string;
    count: number;
  }>;
  locationCounts: {
    local: number;
    ogl: number;
    outstation: number;
  };
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role: User["role"];
  team_id?: string;
  manager_id?: string;
}
