import { toast } from '../hooks/useToast';

// Error types for better categorization
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN'
}

export interface AppError {
  type: ErrorType;
  message: string;
  code?: string;
  details?: any;
  timestamp: string;
  retryable: boolean;
}

// Automatic retry configuration
interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2
};

// Error classification and auto-fixing
export class ErrorHandler {
  private static retryAttempts = new Map<string, number>();
  
  static classifyError(error: any): AppError {
    const timestamp = new Date().toISOString();
    
    // Network errors
    if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
      return {
        type: ErrorType.NETWORK,
        message: 'Network connection failed. Retrying automatically...',
        code: error.code,
        details: error,
        timestamp,
        retryable: true
      };
    }
    
    // HTTP status code errors
    if (error.response?.status) {
      const status = error.response.status;
      
      if (status === 401) {
        return {
          type: ErrorType.AUTHENTICATION,
          message: 'Session expired. Please log in again.',
          code: 'AUTH_EXPIRED',
          details: error,
          timestamp,
          retryable: false
        };
      }
      
      if (status === 403) {
        return {
          type: ErrorType.AUTHORIZATION,
          message: 'Access denied. Insufficient permissions.',
          code: 'ACCESS_DENIED',
          details: error,
          timestamp,
          retryable: false
        };
      }
      
      if (status >= 500) {
        return {
          type: ErrorType.SERVER,
          message: 'Server error. Retrying automatically...',
          code: `SERVER_${status}`,
          details: error,
          timestamp,
          retryable: true
        };
      }
      
      if (status >= 400) {
        return {
          type: ErrorType.VALIDATION,
          message: error.response.data?.message || 'Invalid request data.',
          code: `CLIENT_${status}`,
          details: error,
          timestamp,
          retryable: false
        };
      }
    }
    
    // JavaScript errors
    if (error instanceof TypeError) {
      return {
        type: ErrorType.CLIENT,
        message: 'Application error. Attempting to recover...',
        code: 'TYPE_ERROR',
        details: error,
        timestamp,
        retryable: true
      };
    }
    
    return {
      type: ErrorType.UNKNOWN,
      message: error.message || 'An unexpected error occurred.',
      code: 'UNKNOWN',
      details: error,
      timestamp,
      retryable: true
    };
  }
  
  static async handleError(error: any, context?: string): Promise<void> {
    const appError = this.classifyError(error);
    
    // Log error for monitoring
    console.error(`[${appError.type}] ${context || 'Unknown context'}:`, appError);
    
    // Auto-fix attempts
    await this.attemptAutoFix(appError, context);
    
    // Show user-friendly notification
    this.showUserNotification(appError);
  }
  
  private static async attemptAutoFix(error: AppError, context?: string): Promise<void> {
    switch (error.type) {
      case ErrorType.NETWORK:
        await this.handleNetworkError(error, context);
        break;
        
      case ErrorType.AUTHENTICATION:
        await this.handleAuthError(error);
        break;
        
      case ErrorType.SERVER:
        await this.handleServerError(error, context);
        break;
        
      case ErrorType.CLIENT:
        await this.handleClientError(error, context);
        break;
        
      default:
        // Generic recovery attempt
        await this.genericRecovery(error, context);
    }
  }
  
  private static async handleNetworkError(error: AppError, context?: string): Promise<void> {
    // Check if we can retry
    if (!error.retryable) return;
    
    const key = `${context || 'unknown'}_${error.code}`;
    const attempts = this.retryAttempts.get(key) || 0;
    
    if (attempts < DEFAULT_RETRY_CONFIG.maxAttempts) {
      this.retryAttempts.set(key, attempts + 1);
      
      // Exponential backoff
      const delay = Math.min(
        DEFAULT_RETRY_CONFIG.baseDelay * Math.pow(DEFAULT_RETRY_CONFIG.backoffMultiplier, attempts),
        DEFAULT_RETRY_CONFIG.maxDelay
      );
      
      await new Promise(resolve => setTimeout(resolve, delay));
      
      // Trigger retry by refreshing the page or re-executing the failed operation
      if (attempts === DEFAULT_RETRY_CONFIG.maxAttempts - 1) {
        toast({
          title: "Connection Issues",
          description: "Refreshing page to restore connection...",
          variant: "warning"
        });
        
        setTimeout(() => window.location.reload(), 2000);
      }
    }
  }
  
  private static async handleAuthError(error: AppError): Promise<void> {
    // Clear invalid tokens
    localStorage.removeItem('token');
    sessionStorage.clear();
    
    // Redirect to login
    toast({
      title: "Session Expired",
      description: "Redirecting to login page...",
      variant: "destructive"
    });
    
    setTimeout(() => {
      window.location.href = '/login';
    }, 2000);
  }
  
  private static async handleServerError(error: AppError, context?: string): Promise<void> {
    // Implement circuit breaker pattern
    const key = `server_error_${context}`;
    const attempts = this.retryAttempts.get(key) || 0;
    
    if (attempts < 3) {
      this.retryAttempts.set(key, attempts + 1);
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 2000 * (attempts + 1)));
      
      toast({
        title: "Server Issue",
        description: `Retrying... (${attempts + 1}/3)`,
        variant: "warning"
      });
    } else {
      // Switch to offline mode or cached data
      toast({
        title: "Server Unavailable",
        description: "Switching to offline mode...",
        variant: "destructive"
      });
      
      this.enableOfflineMode();
    }
  }
  
  private static async handleClientError(error: AppError, context?: string): Promise<void> {
    // Try to recover from client-side errors
    try {
      // Clear potentially corrupted state
      if (context?.includes('form')) {
        // Reset form state
        const forms = document.querySelectorAll('form');
        forms.forEach(form => form.reset());
      }
      
      if (context?.includes('storage')) {
        // Clear corrupted localStorage
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith('temp_') || key.startsWith('cache_')) {
            localStorage.removeItem(key);
          }
        });
      }
      
      toast({
        title: "Recovered",
        description: "Application state has been restored.",
        variant: "success"
      });
      
    } catch (recoveryError) {
      console.error('Recovery failed:', recoveryError);
      // Last resort: page refresh
      setTimeout(() => window.location.reload(), 1000);
    }
  }
  
  private static async genericRecovery(error: AppError, context?: string): Promise<void> {
    // Generic recovery strategies
    try {
      // Clear temporary data
      sessionStorage.clear();
      
      // Reset any global state
      if (window.location.pathname !== '/login') {
        // Try to refresh current page data
        window.dispatchEvent(new Event('app-recovery'));
      }
      
    } catch (recoveryError) {
      console.error('Generic recovery failed:', recoveryError);
    }
  }
  
  private static enableOfflineMode(): void {
    // Set offline flag
    localStorage.setItem('offline_mode', 'true');
    
    // Dispatch offline event
    window.dispatchEvent(new CustomEvent('app-offline'));
    
    // Show offline indicator
    const offlineIndicator = document.createElement('div');
    offlineIndicator.id = 'offline-indicator';
    offlineIndicator.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: #f59e0b;
        color: white;
        text-align: center;
        padding: 8px;
        z-index: 9999;
        font-size: 14px;
      ">
        📡 Offline Mode - Limited functionality available
      </div>
    `;
    document.body.appendChild(offlineIndicator);
  }
  
  private static showUserNotification(error: AppError): void {
    const variant = error.retryable ? 'warning' : 'destructive';
    
    toast({
      title: this.getErrorTitle(error.type),
      description: error.message,
      variant: variant as any
    });
  }
  
  private static getErrorTitle(type: ErrorType): string {
    switch (type) {
      case ErrorType.NETWORK:
        return "Connection Issue";
      case ErrorType.AUTHENTICATION:
        return "Authentication Required";
      case ErrorType.AUTHORIZATION:
        return "Access Denied";
      case ErrorType.SERVER:
        return "Server Error";
      case ErrorType.VALIDATION:
        return "Invalid Data";
      case ErrorType.CLIENT:
        return "Application Error";
      default:
        return "Unexpected Error";
    }
  }
  
  // Reset retry attempts for a specific context
  static resetRetryAttempts(context: string): void {
    const keys = Array.from(this.retryAttempts.keys()).filter(key => key.includes(context));
    keys.forEach(key => this.retryAttempts.delete(key));
  }
  
  // Check if system is in offline mode
  static isOfflineMode(): boolean {
    return localStorage.getItem('offline_mode') === 'true';
  }
  
  // Exit offline mode
  static exitOfflineMode(): void {
    localStorage.removeItem('offline_mode');
    const indicator = document.getElementById('offline-indicator');
    if (indicator) {
      indicator.remove();
    }
    window.dispatchEvent(new CustomEvent('app-online'));
  }
}
