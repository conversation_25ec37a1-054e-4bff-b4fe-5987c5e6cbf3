import { ErrorHand<PERSON>, ErrorType } from './errorHandler';
import { toast } from '../hooks/useToast';

interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  url: string;
  data?: any;
  headers?: Record<string, string>;
  timeout?: number;
  retryConfig?: {
    maxAttempts: number;
    baseDelay: number;
    retryCondition?: (error: any) => boolean;
  };
}

interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private requestInterceptors: Array<(config: RequestConfig) => RequestConfig> = [];
  private responseInterceptors: Array<(response: ApiResponse) => ApiResponse> = [];
  private errorInterceptors: Array<(error: any) => Promise<any>> = [];

  constructor(baseURL: string = 'http://localhost:3000/api') {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };

    // Add automatic token injection
    this.addRequestInterceptor((config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${token}`,
        };
      }
      return config;
    });

    // Add automatic error handling
    this.addErrorInterceptor(async (error) => {
      await ErrorHandler.handleError(error, 'api_request');
      throw error;
    });

    // Add network status monitoring
    this.setupNetworkMonitoring();
  }

  private setupNetworkMonitoring(): void {
    window.addEventListener('online', () => {
      ErrorHandler.exitOfflineMode();
      toast({
        title: "Connection Restored",
        description: "You're back online!",
        variant: "success"
      });
    });

    window.addEventListener('offline', () => {
      toast({
        title: "Connection Lost",
        description: "You're now offline. Some features may be limited.",
        variant: "warning"
      });
    });
  }

  addRequestInterceptor(interceptor: (config: RequestConfig) => RequestConfig): void {
    this.requestInterceptors.push(interceptor);
  }

  addResponseInterceptor(interceptor: (response: ApiResponse) => ApiResponse): void {
    this.responseInterceptors.push(interceptor);
  }

  addErrorInterceptor(interceptor: (error: any) => Promise<any>): void {
    this.errorInterceptors.push(interceptor);
  }

  private async executeRequest<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    // Apply request interceptors
    let processedConfig = { ...config };
    for (const interceptor of this.requestInterceptors) {
      processedConfig = interceptor(processedConfig);
    }

    const url = `${this.baseURL}${processedConfig.url}`;
    const headers = { ...this.defaultHeaders, ...processedConfig.headers };

    const fetchConfig: RequestInit = {
      method: processedConfig.method,
      headers,
      signal: AbortSignal.timeout(processedConfig.timeout || 30000),
    };

    if (processedConfig.data && processedConfig.method !== 'GET') {
      fetchConfig.body = JSON.stringify(processedConfig.data);
    }

    try {
      const response = await fetch(url, fetchConfig);
      
      let data: T;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text() as any;
      }

      if (!response.ok) {
        const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
        (error as any).response = {
          status: response.status,
          statusText: response.statusText,
          data,
          headers: Object.fromEntries(response.headers.entries()),
        };
        throw error;
      }

      const apiResponse: ApiResponse<T> = {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
      };

      // Apply response interceptors
      let processedResponse = apiResponse;
      for (const interceptor of this.responseInterceptors) {
        processedResponse = interceptor(processedResponse);
      }

      return processedResponse;

    } catch (error: any) {
      // Apply error interceptors
      for (const interceptor of this.errorInterceptors) {
        try {
          await interceptor(error);
        } catch (interceptorError) {
          console.error('Error interceptor failed:', interceptorError);
        }
      }
      throw error;
    }
  }

  private async requestWithRetry<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    const retryConfig = config.retryConfig || {
      maxAttempts: 3,
      baseDelay: 1000,
      retryCondition: (error: any) => {
        // Retry on network errors or 5xx server errors
        return !error.response || error.response.status >= 500;
      }
    };

    let lastError: any;
    
    for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
      try {
        const response = await this.executeRequest<T>(config);
        
        // Reset retry attempts on success
        ErrorHandler.resetRetryAttempts(`api_${config.url}`);
        
        return response;
      } catch (error: any) {
        lastError = error;
        
        // Check if we should retry
        const shouldRetry = attempt < retryConfig.maxAttempts && 
                           retryConfig.retryCondition!(error);
        
        if (!shouldRetry) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = retryConfig.baseDelay * Math.pow(2, attempt - 1);
        
        console.warn(`Request failed (attempt ${attempt}/${retryConfig.maxAttempts}). Retrying in ${delay}ms...`);
        
        // Show retry notification for user awareness
        if (attempt > 1) {
          toast({
            title: "Retrying Request",
            description: `Attempt ${attempt}/${retryConfig.maxAttempts}`,
            variant: "warning"
          });
        }
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  // Public API methods
  async get<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.requestWithRetry<T>({
      method: 'GET',
      url,
      ...config,
    });
  }

  async post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.requestWithRetry<T>({
      method: 'POST',
      url,
      data,
      ...config,
    });
  }

  async put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.requestWithRetry<T>({
      method: 'PUT',
      url,
      data,
      ...config,
    });
  }

  async delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.requestWithRetry<T>({
      method: 'DELETE',
      url,
      ...config,
    });
  }

  // Utility methods
  setBaseURL(baseURL: string): void {
    this.baseURL = baseURL;
  }

  setDefaultHeader(key: string, value: string): void {
    this.defaultHeaders[key] = value;
  }

  removeDefaultHeader(key: string): void {
    delete this.defaultHeaders[key];
  }

  // Health check with automatic recovery
  async healthCheck(): Promise<boolean> {
    try {
      await this.get('/health', {
        timeout: 5000,
        retryConfig: {
          maxAttempts: 2,
          baseDelay: 1000,
        }
      });
      return true;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  // Batch requests with automatic error recovery
  async batch<T = any>(requests: Array<() => Promise<ApiResponse<T>>>): Promise<Array<ApiResponse<T> | Error>> {
    const results = await Promise.allSettled(requests.map(request => request()));
    
    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        console.error(`Batch request ${index} failed:`, result.reason);
        return result.reason;
      }
    });
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export for custom instances
export { ApiClient };
