/* Modern CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --background: #ffffff;
  --foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #3b82f6;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --radius: 0.5rem;
}

body {
  font-family: "Inter", system-ui, -apple-system, sans-serif;
  background-color: var(--background);
  color: var(--foreground);
  line-height: 1.5;
}

/* Utility Classes */
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.flex-col {
  flex-direction: column;
}
.space-x-2 > * + * {
  margin-left: 0.5rem;
}
.space-y-2 > * + * {
  margin-top: 0.5rem;
}
.space-y-4 > * + * {
  margin-top: 1rem;
}
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.w-full {
  width: 100%;
}
.max-w-md {
  max-width: 28rem;
}
.min-h-screen {
  min-height: 100vh;
}

.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.text-sm {
  font-size: 0.875rem;
}
.text-lg {
  font-size: 1.125rem;
}
.text-xl {
  font-size: 1.25rem;
}
.text-2xl {
  font-size: 1.5rem;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}

.text-center {
  text-align: center;
}
.text-muted-foreground {
  color: var(--muted-foreground);
}

.bg-primary {
  background-color: var(--primary);
}
.bg-secondary {
  background-color: var(--secondary);
}
.bg-muted {
  background-color: var(--muted);
}
.bg-destructive {
  background-color: var(--destructive);
}
.bg-white {
  background-color: white;
}

.text-primary-foreground {
  color: var(--primary-foreground);
}
.text-destructive {
  color: var(--destructive);
}

.border {
  border: 1px solid var(--border);
}
.border-0 {
  border: none;
}
.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}
.rounded-lg {
  border-radius: var(--radius);
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}
.shadow-2xl {
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}

.opacity-50 {
  opacity: 0.5;
}
.cursor-not-allowed {
  cursor: not-allowed;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
  padding: 0.5rem 1rem;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Input Styles */
.input {
  display: flex;
  width: 100%;
  border-radius: calc(var(--radius) - 2px);
  border: 1px solid var(--input);
  background-color: var(--background);
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px var(--ring);
}

/* Card Styles */
.card {
  border-radius: var(--radius);
  border: 1px solid var(--border);
  background-color: var(--background);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

/* Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Responsive */
@media (min-width: 640px) {
  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
