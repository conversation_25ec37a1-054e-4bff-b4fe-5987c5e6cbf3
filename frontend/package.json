{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:prod": "NODE_ENV=production npm run build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "analyze": "npm run build && npx vite-bundle-analyzer dist"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.7", "@tanstack/react-query": "^5.77.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.14.0", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-router-dom": "^7.6.1", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "zod": "^3.25.30"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.21", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-react": "^4.5.0", "@vitejs/plugin-react-swc": "^3.10.0", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "terser": "^5.40.0", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-pwa": "^1.0.0", "workbox-window": "^7.3.0"}}