{"name": "acsbilling-backend", "version": "1.0.0", "description": "Backend for ACS Billing Management System", "main": "dist/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "build": "tsc", "db:init": "node src/database/init.js", "db:reset": "npm run db:init", "lint": "echo 'Add ESLint configuration'", "test": "echo \"Error: no test specified\" && exit 1", "prod": "NODE_ENV=production node src/app.js"}, "keywords": ["billing", "management", "express", "typescript"], "author": "", "license": "ISC", "dependencies": {"@types/pg": "^8.15.2", "argon2": "^0.43.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.16.0", "qrcode": "^1.5.4", "redis": "^5.1.0", "speakeasy": "^2.0.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}