const fs = require("fs");
const path = require("path");
const { query, healthCheck } = require("../config/database");
const bcrypt = require("bcryptjs");

// Database initialization with error handling and performance monitoring
const initializeDatabase = async () => {
  console.log("🚀 Starting database initialization...");

  try {
    // Check database connection
    console.log("📡 Checking database connection...");
    const health = await healthCheck();

    if (health.status !== "healthy") {
      throw new Error(`Database connection failed: ${health.error}`);
    }

    console.log("✅ Database connection established");
    console.log(
      `📊 Pool status: Total: ${health.pool_total}, Idle: ${health.pool_idle}, Waiting: ${health.pool_waiting}`
    );

    // Read and execute schema
    console.log("📋 Creating database schema...");
    const schemaPath = path.join(__dirname, "schema.sql");
    const schema = fs.readFileSync(schemaPath, "utf8");

    // Split schema into individual statements and execute
    const statements = schema
      .split(";")
      .filter((stmt) => stmt.trim().length > 0);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          await query(statement);
          if (i % 10 === 0) {
            console.log(
              `📝 Executed ${i + 1}/${statements.length} statements...`
            );
          }
        } catch (error) {
          // Log but don't fail on conflicts (tables already exist)
          if (
            !error.message.includes("already exists") &&
            !error.message.includes("duplicate key")
          ) {
            console.warn(
              `⚠️  Warning executing statement ${i + 1}: ${error.message}`
            );
          }
        }
      }
    }

    console.log("✅ Database schema created successfully");

    // Create default roles and teams
    console.log("👤 Creating default roles and teams...");
    await createDefaultRolesAndTeams();

    // Create default admin user
    console.log("👤 Creating default users...");
    await createDefaultUsers();

    // Create sample data
    console.log("📊 Creating sample data...");
    await createSampleData();

    console.log("🎉 Database initialization completed successfully!");

    // Final health check
    const finalHealth = await healthCheck();
    console.log(
      `📊 Final pool status: Total: ${finalHealth.pool_total}, Idle: ${finalHealth.pool_idle}, Waiting: ${finalHealth.pool_waiting}`
    );
  } catch (error) {
    console.error("❌ Database initialization failed:", error);
    throw error;
  }
};

// Create default roles and teams
const createDefaultRolesAndTeams = async () => {
  // Default roles
  const defaultRoles = [
    { name: "super_admin" },
    { name: "manager" },
    { name: "team_leader" },
    { name: "employee" },
    { name: "billing_team" },
    { name: "backend" },
    { name: "field_employee" },
  ];

  // Default teams
  const defaultTeams = [
    {
      id: "550e8400-e29b-41d4-a716-446655440001",
      name: "Development Team",
      description: "Main development team",
    },
    {
      id: "550e8400-e29b-41d4-a716-446655440002",
      name: "Billing Team",
      description: "Billing and finance team",
    },
    {
      id: "550e8400-e29b-41d4-a716-446655440003",
      name: "Field Operations",
      description: "Field verification team",
    },
  ];

  // Create roles
  for (const role of defaultRoles) {
    try {
      const existingRole = await query("SELECT id FROM roles WHERE name = $1", [
        role.name,
      ]);

      if (existingRole.rows.length === 0) {
        await query("INSERT INTO roles (name) VALUES ($1)", [role.name]);
        console.log(`✅ Created role: ${role.name}`);
      } else {
        console.log(`ℹ️  Role already exists: ${role.name}`);
      }
    } catch (error) {
      console.error(`❌ Error creating role ${role.name}:`, error.message);
    }
  }

  // Create teams
  for (const team of defaultTeams) {
    try {
      const existingTeam = await query("SELECT id FROM teams WHERE name = $1", [
        team.name,
      ]);

      if (existingTeam.rows.length === 0) {
        await query(
          "INSERT INTO teams (id, name, description) VALUES ($1, $2, $3)",
          [team.id, team.name, team.description]
        );
        console.log(`✅ Created team: ${team.name}`);
      } else {
        console.log(`ℹ️  Team already exists: ${team.name}`);
      }
    } catch (error) {
      console.error(`❌ Error creating team ${team.name}:`, error.message);
    }
  }
};

// Create default users with proper password hashing
const createDefaultUsers = async () => {
  const defaultUsers = [
    {
      email: "<EMAIL>",
      password: "SuperAdmin@2024!",
      name: "Super Administrator",
      role: "super_admin",
      team_id: null, // Super admin doesn't belong to any specific team
    },
    {
      email: "<EMAIL>",
      password: "password",
      name: "Admin User",
      role: "manager",
      team_id: "550e8400-e29b-41d4-a716-446655440001",
    },
    {
      email: "<EMAIL>",
      password: "password",
      name: "John Employee",
      role: "employee",
      team_id: "550e8400-e29b-41d4-a716-446655440001",
    },
    {
      email: "<EMAIL>",
      password: "password",
      name: "Jane Employee",
      role: "employee",
      team_id: "550e8400-e29b-41d4-a716-446655440001",
    },
    {
      email: "<EMAIL>",
      password: "password",
      name: "Team Leader",
      role: "team_leader",
      team_id: "550e8400-e29b-41d4-a716-446655440001",
    },
    {
      email: "<EMAIL>",
      password: "password",
      name: "Billing Team Member",
      role: "billing_team",
      team_id: "550e8400-e29b-41d4-a716-446655440002",
    },
  ];

  for (const user of defaultUsers) {
    try {
      // Check if user already exists
      const existingUser = await query(
        "SELECT id FROM users WHERE email = $1",
        [user.email]
      );

      if (existingUser.rows.length === 0) {
        // Hash password
        const passwordHash = await bcrypt.hash(user.password, 12);

        // Insert user
        const result = await query(
          `
          INSERT INTO users (email, password_hash, name, role, team_id, password_changed_at)
          VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
          RETURNING id
        `,
          [user.email, passwordHash, user.name, user.role, user.team_id]
        );

        console.log(`✅ Created user: ${user.email} (${user.role})`);

        // Create employee assignments for employees
        if (user.role === "employee") {
          await createEmployeeAssignments(result.rows[0].id, user.email);
        }
      } else {
        console.log(`ℹ️  User already exists: ${user.email}`);
      }
    } catch (error) {
      console.error(`❌ Error creating user ${user.email}:`, error.message);
    }
  }
};

// Create employee assignments
const createEmployeeAssignments = async (userId, email) => {
  try {
    // Assign institutes based on employee
    const instituteAssignments = email.includes("employee1")
      ? ["550e8400-e29b-41d4-a716-446655440011"] // Institute A
      : ["550e8400-e29b-41d4-a716-446655440012"]; // Institute B

    // Assign all products to all employees
    const productAssignments = [
      "550e8400-e29b-41d4-a716-446655440021", // Product A
      "550e8400-e29b-41d4-a716-446655440022", // Product B
    ];

    // Create institute assignments
    for (const instituteId of instituteAssignments) {
      await query(
        `
        INSERT INTO employee_institutes (user_id, institute_id)
        VALUES ($1, $2)
        ON CONFLICT (user_id, institute_id) DO NOTHING
      `,
        [userId, instituteId]
      );
    }

    // Create product assignments
    for (const productId of productAssignments) {
      await query(
        `
        INSERT INTO employee_products (user_id, product_id)
        VALUES ($1, $2)
        ON CONFLICT (user_id, product_id) DO NOTHING
      `,
        [userId, productId]
      );
    }

    console.log(`✅ Created assignments for user: ${email}`);
  } catch (error) {
    console.error(`❌ Error creating assignments for ${email}:`, error.message);
  }
};

// Create sample data for testing
const createSampleData = async () => {
  try {
    // Create sample rates
    const rates = [
      {
        role: "employee",
        level: "junior",
        location_type: "local",
        daily_rate: 500.0,
      },
      {
        role: "employee",
        level: "senior",
        location_type: "local",
        daily_rate: 800.0,
      },
      {
        role: "employee",
        level: "junior",
        location_type: "ogl",
        daily_rate: 600.0,
      },
      {
        role: "employee",
        level: "senior",
        location_type: "ogl",
        daily_rate: 900.0,
      },
      {
        role: "employee",
        level: "junior",
        location_type: "outstation",
        daily_rate: 700.0,
      },
      {
        role: "employee",
        level: "senior",
        location_type: "outstation",
        daily_rate: 1000.0,
      },
    ];

    for (const rate of rates) {
      await query(
        `
        INSERT INTO rates (role, level, location_type, daily_rate, hourly_rate, effective_from)
        VALUES ($1, $2, $3, $4, $5, CURRENT_DATE)
        ON CONFLICT DO NOTHING
      `,
        [
          rate.role,
          rate.level,
          rate.location_type,
          rate.daily_rate,
          rate.daily_rate / 8,
        ]
      );
    }

    console.log("✅ Sample rates created");

    // Create sample daily counts
    const users = await query("SELECT id, email FROM users WHERE role = $1", [
      "employee",
    ]);

    for (const user of users.rows) {
      // Get user's assignments
      const institutes = await query(
        "SELECT institute_id FROM employee_institutes WHERE user_id = $1",
        [user.id]
      );
      const products = await query(
        "SELECT product_id FROM employee_products WHERE user_id = $1",
        [user.id]
      );

      if (institutes.rows.length > 0 && products.rows.length > 0) {
        // Create sample daily count
        await query(
          `
          INSERT INTO daily_counts (user_id, institute_id, product_id, service_id, location_type, count, verifier_name, work_date)
          VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_DATE - INTERVAL '1 day')
          ON CONFLICT DO NOTHING
        `,
          [
            user.id,
            institutes.rows[0].institute_id,
            products.rows[0].product_id,
            "550e8400-e29b-41d4-a716-446655440031", // Service A
            "local",
            5,
            "Sample Verifier",
          ]
        );
      }
    }

    console.log("✅ Sample daily counts created");
  } catch (error) {
    console.error("❌ Error creating sample data:", error.message);
  }
};

module.exports = {
  initializeDatabase,
  createDefaultRolesAndTeams,
  createDefaultUsers,
  createSampleData,
};
