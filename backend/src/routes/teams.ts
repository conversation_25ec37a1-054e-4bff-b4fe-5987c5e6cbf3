import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticateToken } from './auth';

const router = express.Router();

// Mock database
let teams: any[] = [
  {
    id: '1',
    name: 'Development Team',
    team_leader_id: '1',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Get all teams
router.get('/', authenticateToken, (req, res) => {
  try {
    res.json({ teams });
  } catch (error) {
    console.error('Get teams error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Create team (managers only)
router.post('/', authenticateToken, [
  body('name').trim().isLength({ min: 2 }),
  body('team_leader_id').notEmpty()
], (req, res) => {
  try {
    const userRole = (req as any).user.role;
    
    if (userRole !== 'manager') {
      return res.status(403).json({ message: 'Access denied' });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, team_leader_id } = req.body;

    // Check if team name already exists
    const existingTeam = teams.find(t => t.name.toLowerCase() === name.toLowerCase());
    if (existingTeam) {
      return res.status(400).json({ message: 'Team name already exists' });
    }

    const newTeam = {
      id: (teams.length + 1).toString(),
      name,
      team_leader_id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    teams.push(newTeam);
    res.status(201).json({ team: newTeam });
  } catch (error) {
    console.error('Create team error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;
