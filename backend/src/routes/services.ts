import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticateToken } from './auth';

const router = express.Router();

// Mock database
let services: any[] = [
  {
    id: '1',
    name: 'Service A',
    description: 'Description for Service A',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '2',
    name: 'Service B',
    description: 'Description for Service B',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Get all services
router.get('/', authenticateToken, (req, res) => {
  try {
    res.json({ services });
  } catch (error) {
    console.error('Get services error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Create service (managers only)
router.post('/', authenticateToken, [
  body('name').trim().isLength({ min: 2 }),
  body('description').trim().isLength({ min: 5 })
], (req, res) => {
  try {
    const userRole = (req as any).user.role;
    
    if (!['manager', 'billing_team'].includes(userRole)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, description } = req.body;

    // Check if service name already exists
    const existingService = services.find(s => s.name.toLowerCase() === name.toLowerCase());
    if (existingService) {
      return res.status(400).json({ message: 'Service name already exists' });
    }

    const newService = {
      id: (services.length + 1).toString(),
      name,
      description,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    services.push(newService);
    res.status(201).json({ service: newService });
  } catch (error) {
    console.error('Create service error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;
