import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticateToken } from './auth';

const router = express.Router();

// Mock database
let dailyCounts: any[] = [];

// Get daily counts for current user
router.get('/', authenticateToken, (req, res) => {
  try {
    const userId = (req as any).user.userId;
    const { date, institute_id, product_id } = req.query;

    let filteredCounts = dailyCounts.filter(count => count.user_id === userId);

    if (date) {
      filteredCounts = filteredCounts.filter(count => count.date === date);
    }
    if (institute_id) {
      filteredCounts = filteredCounts.filter(count => count.institute_id === institute_id);
    }
    if (product_id) {
      filteredCounts = filteredCounts.filter(count => count.product_id === product_id);
    }

    res.json({ counts: filteredCounts });
  } catch (error) {
    console.error('Get daily counts error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Get all daily counts (for managers and billing team)
router.get('/all', authenticateToken, (req, res) => {
  try {
    const userRole = (req as any).user.role;
    
    if (!['manager', 'billing_team', 'team_leader'].includes(userRole)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const { date, team_id, institute_id, user_id } = req.query;
    let filteredCounts = [...dailyCounts];

    if (date) {
      filteredCounts = filteredCounts.filter(count => count.date === date);
    }
    if (team_id) {
      // In a real app, you'd join with users table to filter by team
      filteredCounts = filteredCounts.filter(count => count.team_id === team_id);
    }
    if (institute_id) {
      filteredCounts = filteredCounts.filter(count => count.institute_id === institute_id);
    }
    if (user_id) {
      filteredCounts = filteredCounts.filter(count => count.user_id === user_id);
    }

    res.json({ counts: filteredCounts });
  } catch (error) {
    console.error('Get all daily counts error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Add daily count
router.post('/', authenticateToken, [
  body('institute_id').notEmpty(),
  body('product_id').notEmpty(),
  body('service_id').notEmpty(),
  body('count').isInt({ min: 0 }),
  body('location_type').isIn(['local', 'ogl', 'outstation']),
  body('verifier_name').trim().isLength({ min: 2 }),
  body('date').isISO8601()
], (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const userId = (req as any).user.userId;
    const { institute_id, product_id, service_id, count, location_type, verifier_name, date } = req.body;

    // Check if entry already exists for this date
    const existingEntry = dailyCounts.find(entry => 
      entry.user_id === userId &&
      entry.institute_id === institute_id &&
      entry.product_id === product_id &&
      entry.service_id === service_id &&
      entry.date === date
    );

    if (existingEntry) {
      return res.status(400).json({ message: 'Entry already exists for this date' });
    }

    const newCount = {
      id: (dailyCounts.length + 1).toString(),
      user_id: userId,
      institute_id,
      product_id,
      service_id,
      count: parseInt(count),
      location_type,
      verifier_name,
      date,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    dailyCounts.push(newCount);
    res.status(201).json({ count: newCount });
  } catch (error) {
    console.error('Add daily count error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Update daily count
router.put('/:id', authenticateToken, [
  body('count').isInt({ min: 0 }),
  body('location_type').isIn(['local', 'ogl', 'outstation']),
  body('verifier_name').trim().isLength({ min: 2 })
], (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const userId = (req as any).user.userId;
    const userRole = (req as any).user.role;
    const { count, location_type, verifier_name } = req.body;

    const countIndex = dailyCounts.findIndex(c => c.id === id);
    if (countIndex === -1) {
      return res.status(404).json({ message: 'Count not found' });
    }

    const existingCount = dailyCounts[countIndex];

    // Check if user owns this count or has permission to edit
    if (existingCount.user_id !== userId && !['manager', 'team_leader'].includes(userRole)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Update the count
    dailyCounts[countIndex] = {
      ...existingCount,
      count: parseInt(count),
      location_type,
      verifier_name,
      updated_at: new Date().toISOString()
    };

    res.json({ count: dailyCounts[countIndex] });
  } catch (error) {
    console.error('Update daily count error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Delete daily count
router.delete('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const userId = (req as any).user.userId;
    const userRole = (req as any).user.role;

    const countIndex = dailyCounts.findIndex(c => c.id === id);
    if (countIndex === -1) {
      return res.status(404).json({ message: 'Count not found' });
    }

    const existingCount = dailyCounts[countIndex];

    // Check if user owns this count or has permission to delete
    if (existingCount.user_id !== userId && !['manager', 'team_leader'].includes(userRole)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    dailyCounts.splice(countIndex, 1);
    res.json({ message: 'Count deleted successfully' });
  } catch (error) {
    console.error('Delete daily count error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;
