const express = require("express");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const { body, validationResult } = require("express-validator");
const {
  authLimiter,
  validateInput,
  emailValidation,
  passwordValidation,
  nameValidation,
  hashPassword,
  verifyPassword,
} = require("../middleware/security");

const router = express.Router();

// Mock database - In production, use a real database
let users = [
  {
    id: "0",
    email: "<EMAIL>",
    password_hash:
      "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
    name: "Super Administrator",
    role: "super_admin",
    team_id: null,
    manager_id: null,
    loginAttempts: 0,
    lockUntil: null,
    twoFactorEnabled: false,
    twoFactorSecret: null,
    lastLogin: null,
    passwordChangedAt: new Date().toISOString(),
  },
  {
    id: "1",
    email: "<EMAIL>",
    password_hash:
      "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
    name: "Admin User",
    role: "manager",
    team_id: null,
    manager_id: null,
    loginAttempts: 0,
    lockUntil: null,
    twoFactorEnabled: false,
    twoFactorSecret: null,
    lastLogin: null,
    passwordChangedAt: new Date().toISOString(),
  },
  {
    id: "2",
    email: "<EMAIL>",
    password_hash:
      "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
    name: "John Employee",
    role: "employee",
    team_id: "1",
    manager_id: "1",
    loginAttempts: 0,
    lockUntil: null,
    twoFactorEnabled: false,
    twoFactorSecret: null,
    lastLogin: null,
    passwordChangedAt: new Date().toISOString(),
  },
  {
    id: "3",
    email: "<EMAIL>",
    password_hash:
      "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
    name: "Jane Employee",
    role: "employee",
    team_id: "1",
    manager_id: "1",
    loginAttempts: 0,
    lockUntil: null,
    twoFactorEnabled: false,
    twoFactorSecret: null,
    lastLogin: null,
    passwordChangedAt: new Date().toISOString(),
  },
  {
    id: "4",
    email: "<EMAIL>",
    password_hash:
      "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
    name: "Team Leader",
    role: "team_leader",
    team_id: "1",
    manager_id: "1",
    loginAttempts: 0,
    lockUntil: null,
    twoFactorEnabled: false,
    twoFactorSecret: null,
    lastLogin: null,
    passwordChangedAt: new Date().toISOString(),
  },
  {
    id: "5",
    email: "<EMAIL>",
    password_hash:
      "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
    name: "Billing Team Member",
    role: "billing_team",
    team_id: "2",
    manager_id: "1",
    loginAttempts: 0,
    lockUntil: null,
    twoFactorEnabled: false,
    twoFactorSecret: null,
    lastLogin: null,
    passwordChangedAt: new Date().toISOString(),
  },
];

// Register
router.post(
  "/register",
  authLimiter,
  validateInput([
    emailValidation,
    passwordValidation,
    nameValidation,
    body("role")
      .isIn([
        "super_admin",
        "employee",
        "team_leader",
        "manager",
        "billing_team",
        "backend",
        "field_employee",
      ])
      .withMessage("Invalid role specified"),
  ]),
  async (req, res) => {
    try {
      const { email, password, name, role, team_id, manager_id } = req.body;

      // Check if user already exists
      const existingUser = users.find((u) => u.email === email);
      if (existingUser) {
        return res.status(400).json({ message: "User already exists" });
      }

      // Hash password with Argon2
      const password_hash = await hashPassword(password);

      // Create user
      const newUser = {
        id: (users.length + 1).toString(),
        email,
        password_hash,
        name,
        role,
        team_id: team_id || null,
        manager_id: manager_id || null,
        loginAttempts: 0,
        lockUntil: null,
        twoFactorEnabled: false,
        twoFactorSecret: null,
        lastLogin: null,
        passwordChangedAt: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      users.push(newUser);

      // Generate JWT
      const token = jwt.sign(
        { userId: newUser.id, email: newUser.email, role: newUser.role },
        process.env.JWT_SECRET || "fallback_secret",
        { expiresIn: "24h" }
      );

      // Return user without password
      const { password_hash: _, ...userWithoutPassword } = newUser;
      res.status(201).json({ user: userWithoutPassword, token });
    } catch (error) {
      console.error("Registration error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  }
);

// Login
router.post(
  "/login",
  authLimiter,
  validateInput([
    emailValidation,
    body("password").notEmpty().withMessage("Password is required"),
  ]),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { email, password } = req.body;

      // Find user
      const user = users.find((u) => u.email === email);
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Check password
      const isValidPassword = await bcrypt.compare(
        password,
        user.password_hash
      );
      if (!isValidPassword) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Generate JWT
      const token = jwt.sign(
        { userId: user.id, email: user.email, role: user.role },
        process.env.JWT_SECRET || "fallback_secret",
        { expiresIn: "24h" }
      );

      // Return user without password
      const { password_hash: _, ...userWithoutPassword } = user;
      res.json({ user: userWithoutPassword, token });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  }
);

// Get current user
router.get("/me", authenticateToken, (req, res) => {
  const user = users.find((u) => u.id === req.user.userId);
  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }

  const { password_hash: _, ...userWithoutPassword } = user;
  res.json({ user: userWithoutPassword });
});

// Middleware to authenticate JWT token
function authenticateToken(req, res, next) {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1];

  if (!token) {
    return res.status(401).json({ message: "Access token required" });
  }

  jwt.verify(
    token,
    process.env.JWT_SECRET || "fallback_secret",
    (err, user) => {
      if (err) {
        return res.status(403).json({ message: "Invalid token" });
      }
      req.user = user;
      next();
    }
  );
}

module.exports = { router, authenticateToken };
