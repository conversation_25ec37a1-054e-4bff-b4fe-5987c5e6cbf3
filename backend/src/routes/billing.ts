import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticateToken } from './auth';

const router = express.Router();

// Mock database
let billingFormats: any[] = [
  {
    id: '1',
    institute_id: '1',
    format_template: 'Standard billing format for {institute_name}',
    rates: {
      'local': 100,
      'ogl': 120,
      'outstation': 150
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Get billing formats
router.get('/formats', authenticateToken, (req, res) => {
  try {
    const userRole = (req as any).user.role;
    
    if (!['manager', 'billing_team'].includes(userRole)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json({ formats: billingFormats });
  } catch (error) {
    console.error('Get billing formats error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Create/Update billing format
router.post('/formats', authenticateToken, [
  body('institute_id').notEmpty(),
  body('format_template').trim().isLength({ min: 10 }),
  body('rates').isObject()
], (req, res) => {
  try {
    const userRole = (req as any).user.role;
    
    if (!['manager', 'billing_team'].includes(userRole)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { institute_id, format_template, rates } = req.body;

    // Check if format already exists for this institute
    const existingFormatIndex = billingFormats.findIndex(f => f.institute_id === institute_id);
    
    if (existingFormatIndex !== -1) {
      // Update existing format
      billingFormats[existingFormatIndex] = {
        ...billingFormats[existingFormatIndex],
        format_template,
        rates,
        updated_at: new Date().toISOString()
      };
      res.json({ format: billingFormats[existingFormatIndex] });
    } else {
      // Create new format
      const newFormat = {
        id: (billingFormats.length + 1).toString(),
        institute_id,
        format_template,
        rates,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      billingFormats.push(newFormat);
      res.status(201).json({ format: newFormat });
    }
  } catch (error) {
    console.error('Create/Update billing format error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Generate bill
router.post('/generate', authenticateToken, [
  body('institute_id').notEmpty(),
  body('start_date').isISO8601(),
  body('end_date').isISO8601()
], (req, res) => {
  try {
    const userRole = (req as any).user.role;
    
    if (userRole !== 'billing_team') {
      return res.status(403).json({ message: 'Access denied - Billing team only' });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { institute_id, start_date, end_date } = req.body;

    // Mock bill generation logic
    const mockBillData = {
      bill_id: `BILL-${Date.now()}`,
      institute_id,
      period: `${start_date} to ${end_date}`,
      items: [
        {
          product: 'Product A',
          service: 'Service A',
          location_type: 'local',
          quantity: 10,
          rate: 100,
          amount: 1000
        },
        {
          product: 'Product B',
          service: 'Service B',
          location_type: 'ogl',
          quantity: 5,
          rate: 120,
          amount: 600
        }
      ],
      subtotal: 1600,
      tax: 160,
      total: 1760,
      generated_at: new Date().toISOString(),
      generated_by: (req as any).user.userId
    };

    res.json({ bill: mockBillData });
  } catch (error) {
    console.error('Generate bill error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;
