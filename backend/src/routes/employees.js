const express = require("express");
const { body, validationResult } = require("express-validator");
const errorRecoveryManager = require("../middleware/errorRecovery");
const { query } = require("../config/database");

const router = express.Router();

// In-memory storage for demo (in production, use a database)
let employees = [];

let nextId = 1;

// Validation rules with auto-correction
const employeeValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Name must be between 2 and 100 characters")
    .customSanitizer((value) => {
      // Auto-fix: Capitalize first letter of each word
      return value.replace(/\b\w/g, (char) => char.toUpperCase());
    }),

  body("email")
    .isEmail()
    .normalizeEmail()
    .withMessage("Please provide a valid email address")
    .customSanitizer((value) => {
      // Auto-fix: Convert to lowercase
      return value.toLowerCase();
    }),

  body("role")
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage("Role must be between 2 and 50 characters"),

  body("team")
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage("Team name must be between 2 and 50 characters"),

  // Institutes and Products are optional for field_employee role (arrays)
  body("institutes")
    .optional()
    .isArray()
    .withMessage("Institutes must be an array")
    .custom((value, { req }) => {
      // Require institutes for all roles except field_employee
      if (
        req.body.role !== "field_employee" &&
        (!value || !Array.isArray(value) || value.length === 0)
      ) {
        throw new Error("At least one institute is required for this role");
      }
      return true;
    }),

  body("products")
    .optional()
    .isArray()
    .withMessage("Products must be an array")
    .custom((value, { req }) => {
      // Require products for all roles except field_employee
      if (
        req.body.role !== "field_employee" &&
        (!value || !Array.isArray(value) || value.length === 0)
      ) {
        throw new Error("At least one product is required for this role");
      }
      return true;
    }),
];

// GET /api/employees - Get all employees with filtering and pagination
router.get("/", async (req, res) => {
  try {
    // Check circuit breaker
    if (!errorRecoveryManager.checkCircuitBreaker("api")) {
      return res.status(503).json({
        error: "Service temporarily unavailable",
        message: "API service is recovering. Please try again later.",
      });
    }

    const {
      page = 1,
      limit = 10,
      search = "",
      role = "",
      team = "",
      isActive = "",
    } = req.query;

    // Fetch employees from database
    let dbQuery = `
      SELECT u.id, u.email, u.name, u.role, t.name as team_name, u.is_active, u.created_at, u.updated_at
      FROM users u
      LEFT JOIN teams t ON u.team_id = t.id
      WHERE 1=1
    `;
    let queryParams = [];
    let paramCount = 0;

    // Add filters
    if (search) {
      paramCount++;
      dbQuery += ` AND (LOWER(u.name) LIKE $${paramCount} OR LOWER(u.email) LIKE $${paramCount})`;
      queryParams.push(`%${search.toLowerCase()}%`);
    }

    if (role) {
      paramCount++;
      dbQuery += ` AND u.role = $${paramCount}`;
      queryParams.push(role);
    }

    if (team) {
      paramCount++;
      dbQuery += ` AND t.name = $${paramCount}`;
      queryParams.push(team);
    }

    if (isActive !== "") {
      paramCount++;
      dbQuery += ` AND u.is_active = $${paramCount}`;
      queryParams.push(isActive === "true");
    }

    dbQuery += ` ORDER BY u.created_at DESC`;

    const dbResult = await query(dbQuery, queryParams);

    // Convert database results to frontend format
    let filteredEmployees = dbResult.rows.map((row) => ({
      id: row.id,
      name: row.name,
      email: row.email,
      role: row.role,
      team: row.team_name || "",
      institutes: [], // Will be populated from assignments (array)
      products: [], // Will be populated from assignments (array)
      isActive: row.is_active,
      joinDate: row.created_at.toISOString().split("T")[0],
      createdAt: row.created_at.toISOString(),
      updatedAt: row.updated_at.toISOString(),
    }));

    // Pagination with auto-correction
    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(100, Math.max(1, parseInt(limit)));
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    const paginatedEmployees = filteredEmployees.slice(startIndex, endIndex);

    // Record success for circuit breaker
    errorRecoveryManager.recordSuccess("api");

    res.json({
      employees: paginatedEmployees,
      pagination: {
        currentPage: pageNum,
        totalPages: Math.ceil(filteredEmployees.length / limitNum),
        totalItems: filteredEmployees.length,
        itemsPerPage: limitNum,
      },
      filters: { search, role, team, isActive },
    });
  } catch (error) {
    errorRecoveryManager.recordFailure("api");
    console.error("Get employees error:", error);

    res.status(500).json({
      error: "Failed to retrieve employees",
      message: "An error occurred while fetching employee data",
      autoRecovery: "System is attempting automatic recovery",
    });
  }
});

// Role validation (simplified)
const roleValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage("Role name must be between 2 and 50 characters")
    .customSanitizer((value) => {
      // Auto-fix: Convert to lowercase and replace spaces with underscores
      return value.toLowerCase().replace(/\s+/g, "_");
    }),
];

// Team validation
const teamValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Team name must be between 2 and 100 characters"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage("Description must be less than 255 characters"),
];

// GET /api/employees/roles - Get all roles
router.get("/roles", async (req, res) => {
  try {
    const { activeOnly = "false" } = req.query;

    let whereClause = "";
    if (activeOnly === "true") {
      whereClause = "WHERE is_active = true";
    }

    const result = await query(
      `SELECT id, name, is_active, created_at, updated_at FROM roles ${whereClause} ORDER BY name`
    );

    res.json({
      roles: result.rows,
      total: result.rows.length,
    });
  } catch (error) {
    console.error("Get roles error:", error);
    res.status(500).json({
      error: "Failed to fetch roles",
      message: "An error occurred while fetching roles",
    });
  }
});

// POST /api/employees/roles - Create new role
router.post("/roles", roleValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, isActive } = req.body;

    // Check for duplicate role name
    const existingRole = await query("SELECT id FROM roles WHERE name = $1", [
      name,
    ]);

    if (existingRole.rows.length > 0) {
      return res.status(409).json({
        error: "Role already exists",
        message: `Role with name '${name}' already exists`,
      });
    }

    // Handle isActive field - default to true if not provided
    const activeStatus = isActive !== undefined ? isActive : true;

    // Insert new role
    const result = await query(
      "INSERT INTO roles (name, is_active) VALUES ($1, $2) RETURNING id, name, is_active, created_at, updated_at",
      [name, activeStatus]
    );

    res.status(201).json({
      message: "Role created successfully",
      role: result.rows[0],
    });
  } catch (error) {
    console.error("Create role error:", error);
    res.status(500).json({
      error: "Failed to create role",
      message: "An error occurred while creating the role",
    });
  }
});

// GET /api/employees/teams - Get all teams
router.get("/teams", async (req, res) => {
  try {
    const { activeOnly = "false" } = req.query;

    let whereClause = "";
    if (activeOnly === "true") {
      whereClause = "WHERE is_active = true";
    }

    const result = await query(
      `SELECT id, name, description, manager_id, is_active, created_at, updated_at FROM teams ${whereClause} ORDER BY name`
    );

    res.json({
      teams: result.rows,
      total: result.rows.length,
    });
  } catch (error) {
    console.error("Get teams error:", error);
    res.status(500).json({
      error: "Failed to fetch teams",
      message: "An error occurred while fetching teams",
    });
  }
});

// POST /api/employees/teams - Create new team
router.post("/teams", teamValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, description = "", isActive } = req.body;

    // Check for duplicate team name
    const existingTeam = await query(
      "SELECT id FROM teams WHERE LOWER(name) = LOWER($1)",
      [name]
    );

    if (existingTeam.rows.length > 0) {
      return res.status(409).json({
        error: "Team already exists",
        message: `Team with name '${name}' already exists`,
      });
    }

    // Handle isActive field - default to true if not provided
    const activeStatus = isActive !== undefined ? isActive : true;

    // Insert new team
    const result = await query(
      "INSERT INTO teams (name, description, is_active) VALUES ($1, $2, $3) RETURNING id, name, description, manager_id, is_active, created_at, updated_at",
      [name, description, activeStatus]
    );

    res.status(201).json({
      message: "Team created successfully",
      team: result.rows[0],
    });
  } catch (error) {
    console.error("Create team error:", error);
    res.status(500).json({
      error: "Failed to create team",
      message: "An error occurred while creating the team",
    });
  }
});

// PUT /api/employees/roles/:id - Update role
router.put("/roles/:id", roleValidation, async (req, res) => {
  try {
    const { id } = req.params;
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, isActive } = req.body;

    // Check if role exists
    const existingRole = await query("SELECT id FROM roles WHERE id = $1", [
      id,
    ]);
    if (existingRole.rows.length === 0) {
      return res.status(404).json({
        error: "Role not found",
        message: `No role found with ID: ${id}`,
      });
    }

    // Check for duplicate role name (excluding current role)
    const duplicateRole = await query(
      "SELECT id FROM roles WHERE name = $1 AND id != $2",
      [name, id]
    );

    if (duplicateRole.rows.length > 0) {
      return res.status(409).json({
        error: "Role name already exists",
        message: `Role with name '${name}' already exists`,
      });
    }

    // Handle isActive field - default to true if not provided
    const activeStatus = isActive !== undefined ? isActive : true;

    // Update role
    const result = await query(
      "UPDATE roles SET name = $1, is_active = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3 RETURNING id, name, is_active, created_at, updated_at",
      [name, activeStatus, id]
    );

    res.json({
      message: "Role updated successfully",
      role: result.rows[0],
    });
  } catch (error) {
    console.error("Update role error:", error);
    res.status(500).json({
      error: "Failed to update role",
      message: "An error occurred while updating the role",
    });
  }
});

// DELETE /api/employees/roles/:id - Delete role (soft delete)
router.delete("/roles/:id", async (req, res) => {
  try {
    const { id } = req.params;

    // Check if role exists
    const existingRole = await query(
      "SELECT id, name FROM roles WHERE id = $1",
      [id]
    );
    if (existingRole.rows.length === 0) {
      return res.status(404).json({
        error: "Role not found",
        message: `No role found with ID: ${id}`,
      });
    }

    // Check if role is being used by any ACTIVE employees
    const activeUsersWithRole = await query(
      "SELECT COUNT(*) as count FROM users WHERE role = $1 AND is_active = true",
      [existingRole.rows[0].name]
    );

    const allUsersWithRole = await query(
      "SELECT COUNT(*) as count FROM users WHERE role = $1",
      [existingRole.rows[0].name]
    );

    const activeCount = parseInt(activeUsersWithRole.rows[0].count);
    const totalCount = parseInt(allUsersWithRole.rows[0].count);
    const inactiveCount = totalCount - activeCount;

    if (activeCount > 0) {
      return res.status(400).json({
        error: "Role is in use by active employees",
        message: `Cannot delete role '${
          existingRole.rows[0].name
        }' as it is assigned to ${activeCount} active employee(s)${
          inactiveCount > 0 ? ` and ${inactiveCount} inactive employee(s)` : ""
        }. Please reassign or deactivate these employees first.`,
        details: {
          activeEmployees: activeCount,
          inactiveEmployees: inactiveCount,
          totalEmployees: totalCount,
        },
      });
    }

    // If only inactive employees use this role, allow deletion but warn user
    if (inactiveCount > 0) {
      // Update inactive employees to remove the role assignment
      await query(
        "UPDATE users SET role = 'unassigned', updated_at = CURRENT_TIMESTAMP WHERE role = $1 AND is_active = false",
        [existingRole.rows[0].name]
      );
    }

    // Soft delete role
    await query(
      "UPDATE roles SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1",
      [id]
    );

    res.json({
      message: "Role deleted successfully",
      note:
        inactiveCount > 0
          ? `Role has been soft-deleted. ${inactiveCount} inactive employee(s) were reassigned to 'unassigned' role.`
          : "Role has been soft-deleted (deactivated)",
      details: {
        inactiveEmployeesReassigned: inactiveCount,
      },
    });
  } catch (error) {
    console.error("Delete role error:", error);
    res.status(500).json({
      error: "Failed to delete role",
      message: "An error occurred while deleting the role",
    });
  }
});

// PUT /api/employees/teams/:id - Update team
router.put("/teams/:id", teamValidation, async (req, res) => {
  try {
    const { id } = req.params;
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, description = "", isActive } = req.body;

    // Check if team exists
    const existingTeam = await query("SELECT id FROM teams WHERE id = $1", [
      id,
    ]);
    if (existingTeam.rows.length === 0) {
      return res.status(404).json({
        error: "Team not found",
        message: `No team found with ID: ${id}`,
      });
    }

    // Check for duplicate team name (excluding current team)
    const duplicateTeam = await query(
      "SELECT id FROM teams WHERE LOWER(name) = LOWER($1) AND id != $2",
      [name, id]
    );

    if (duplicateTeam.rows.length > 0) {
      return res.status(409).json({
        error: "Team name already exists",
        message: `Team with name '${name}' already exists`,
      });
    }

    // Handle isActive field - default to true if not provided
    const activeStatus = isActive !== undefined ? isActive : true;

    // Update team
    const result = await query(
      "UPDATE teams SET name = $1, description = $2, is_active = $3, updated_at = CURRENT_TIMESTAMP WHERE id = $4 RETURNING id, name, description, manager_id, is_active, created_at, updated_at",
      [name, description, activeStatus, id]
    );

    res.json({
      message: "Team updated successfully",
      team: result.rows[0],
    });
  } catch (error) {
    console.error("Update team error:", error);
    res.status(500).json({
      error: "Failed to update team",
      message: "An error occurred while updating the team",
    });
  }
});

// DELETE /api/employees/teams/:id - Delete team (soft delete)
router.delete("/teams/:id", async (req, res) => {
  try {
    const { id } = req.params;

    // Check if team exists
    const existingTeam = await query(
      "SELECT id, name FROM teams WHERE id = $1",
      [id]
    );
    if (existingTeam.rows.length === 0) {
      return res.status(404).json({
        error: "Team not found",
        message: `No team found with ID: ${id}`,
      });
    }

    // Check if team is being used by any ACTIVE employees
    const activeUsersInTeam = await query(
      "SELECT COUNT(*) as count FROM users WHERE team_id = $1 AND is_active = true",
      [id]
    );

    const allUsersInTeam = await query(
      "SELECT COUNT(*) as count FROM users WHERE team_id = $1",
      [id]
    );

    const activeCount = parseInt(activeUsersInTeam.rows[0].count);
    const totalCount = parseInt(allUsersInTeam.rows[0].count);
    const inactiveCount = totalCount - activeCount;

    if (activeCount > 0) {
      return res.status(400).json({
        error: "Team is in use by active employees",
        message: `Cannot delete team '${
          existingTeam.rows[0].name
        }' as it has ${activeCount} active employee(s)${
          inactiveCount > 0 ? ` and ${inactiveCount} inactive employee(s)` : ""
        } assigned. Please reassign or deactivate these employees first.`,
        details: {
          activeEmployees: activeCount,
          inactiveEmployees: inactiveCount,
          totalEmployees: totalCount,
        },
      });
    }

    // If only inactive employees are in this team, allow deletion but reassign them
    if (inactiveCount > 0) {
      // Get a default team to reassign inactive employees to
      const defaultTeam = await query(
        "SELECT id FROM teams WHERE name = 'Unassigned' AND is_active = true"
      );

      let defaultTeamId = null;
      if (defaultTeam.rows.length === 0) {
        // Create 'Unassigned' team if it doesn't exist
        const newTeamResult = await query(
          "INSERT INTO teams (name, description) VALUES ($1, $2) RETURNING id",
          ["Unassigned", "Default team for unassigned employees"]
        );
        defaultTeamId = newTeamResult.rows[0].id;
      } else {
        defaultTeamId = defaultTeam.rows[0].id;
      }

      // Update inactive employees to the default team
      await query(
        "UPDATE users SET team_id = $1, updated_at = CURRENT_TIMESTAMP WHERE team_id = $2 AND is_active = false",
        [defaultTeamId, id]
      );
    }

    // Soft delete team
    await query(
      "UPDATE teams SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1",
      [id]
    );

    res.json({
      message: "Team deleted successfully",
      note:
        inactiveCount > 0
          ? `Team has been soft-deleted. ${inactiveCount} inactive employee(s) were reassigned to 'Unassigned' team.`
          : "Team has been soft-deleted (deactivated)",
      details: {
        inactiveEmployeesReassigned: inactiveCount,
      },
    });
  } catch (error) {
    console.error("Delete team error:", error);
    res.status(500).json({
      error: "Failed to delete team",
      message: "An error occurred while deleting the team",
    });
  }
});

// GET /api/employees/:id - Get employee by ID
router.get("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const employee = employees.find((emp) => emp.id === id);

    if (!employee) {
      return res.status(404).json({
        error: "Employee not found",
        message: `No employee found with ID: ${id}`,
      });
    }

    res.json({ employee });
  } catch (error) {
    console.error("Get employee error:", error);
    res.status(500).json({
      error: "Failed to retrieve employee",
      message: "An error occurred while fetching employee data",
    });
  }
});

// POST /api/employees - Create new employee
router.post("/", employeeValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
        autoFix: "Some fields have been automatically corrected",
      });
    }

    const {
      name,
      email,
      role,
      team,
      institutes = [],
      products = [],
    } = req.body;

    // Check for duplicate email in database
    const existingUser = await query("SELECT id FROM users WHERE email = $1", [
      email,
    ]);

    if (existingUser.rows.length > 0) {
      return res.status(409).json({
        error: "Email already exists",
        message: `Employee with email ${email} already exists`,
        suggestion: `Try: ${email.split("@")[0]}${Date.now()}@${
          email.split("@")[1]
        }`,
      });
    }

    // Get role and team IDs from database
    const roleResult = await query(
      "SELECT id FROM roles WHERE name = $1 AND is_active = true",
      [role]
    );

    const teamResult = await query(
      "SELECT id FROM teams WHERE name = $1 AND is_active = true",
      [team]
    );

    if (roleResult.rows.length === 0) {
      return res.status(400).json({
        error: "Invalid role",
        message: `Role '${role}' not found`,
      });
    }

    if (teamResult.rows.length === 0) {
      return res.status(400).json({
        error: "Invalid team",
        message: `Team '${team}' not found`,
      });
    }

    const teamId = teamResult.rows[0].id;

    // Create new user in database
    const userResult = await query(
      `INSERT INTO users (email, password_hash, name, role, team_id, is_active)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING id, email, name, role, team_id, is_active, created_at, updated_at`,
      [email, "temp_password_hash", name, role, teamId, true]
    );

    const newUser = userResult.rows[0];

    // Create employee object for response (matching frontend expectations)
    const newEmployee = {
      id: newUser.id,
      name: newUser.name,
      email: newUser.email,
      role: newUser.role,
      team: team, // Use team name for frontend
      institutes: institutes, // Array of institutes
      products: products, // Array of products
      isActive: newUser.is_active,
      joinDate: newUser.created_at.toISOString().split("T")[0],
      createdAt: newUser.created_at.toISOString(),
      updatedAt: newUser.updated_at.toISOString(),
    };

    // Also add to in-memory storage for backward compatibility
    employees.push(newEmployee);

    res.status(201).json({
      message: "Employee created successfully",
      employee: newEmployee,
      autoCorrections: "Name capitalized, email normalized, saved to database",
    });
  } catch (error) {
    console.error("Create employee error:", error);
    res.status(500).json({
      error: "Failed to create employee",
      message: "An error occurred while creating the employee",
    });
  }
});

// PUT /api/employees/:id - Update employee
router.put("/:id", employeeValidation, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if employee exists in database
    const existingEmployee = await query(
      "SELECT id, name, email FROM users WHERE id = $1",
      [id]
    );

    if (existingEmployee.rows.length === 0) {
      return res.status(404).json({
        error: "Employee not found",
        message: `No employee found with ID: ${id}`,
      });
    }

    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const {
      name,
      email,
      role,
      team,
      institutes = [],
      products = [],
      isActive,
    } = req.body;

    // Check for duplicate email (excluding current employee)
    const duplicateEmail = await query(
      "SELECT id FROM users WHERE email = $1 AND id != $2",
      [email, id]
    );

    if (duplicateEmail.rows.length > 0) {
      return res.status(409).json({
        error: "Email already exists",
        message: `Another employee with email ${email} already exists`,
      });
    }

    // Find team ID if team name is provided
    let teamId = null;
    if (team) {
      const teamResult = await query("SELECT id FROM teams WHERE name = $1", [
        team,
      ]);
      if (teamResult.rows.length > 0) {
        teamId = teamResult.rows[0].id;
      }
    }

    // Update employee in database
    // Ensure isActive is properly handled - default to true if not provided
    const activeStatus = isActive !== undefined ? isActive : true;

    const result = await query(
      "UPDATE users SET name = $1, email = $2, role = $3, team_id = $4, is_active = $5, updated_at = CURRENT_TIMESTAMP WHERE id = $6 RETURNING id, name, email, role, is_active, updated_at",
      [name, email, role, teamId, activeStatus, id]
    );

    // Create updated employee object for response (matching frontend expectations)
    const updatedEmployee = {
      id: result.rows[0].id,
      name: result.rows[0].name,
      email: result.rows[0].email,
      role: result.rows[0].role,
      team: team, // Use team name for frontend
      institutes: institutes, // Array of institutes
      products: products, // Array of products
      isActive: result.rows[0].is_active,
      joinDate: result.rows[0].created_at
        ? result.rows[0].created_at.toISOString().split("T")[0]
        : new Date().toISOString().split("T")[0],
      createdAt: result.rows[0].created_at
        ? result.rows[0].created_at.toISOString()
        : new Date().toISOString(),
      updatedAt: result.rows[0].updated_at.toISOString(),
    };

    res.json({
      message: "Employee updated successfully",
      employee: updatedEmployee,
      autoCorrections: "Employee data updated successfully",
    });
  } catch (error) {
    console.error("Update employee error:", error);
    res.status(500).json({
      error: "Failed to update employee",
      message: "An error occurred while updating the employee",
    });
  }
});

// DELETE /api/employees/:id - Delete employee (soft delete)
router.delete("/:id", async (req, res) => {
  try {
    const { id } = req.params;

    // Check if employee exists in database
    const existingEmployee = await query(
      "SELECT id, name, email FROM users WHERE id = $1",
      [id]
    );

    if (existingEmployee.rows.length === 0) {
      return res.status(404).json({
        error: "Employee not found",
        message: `No employee found with ID: ${id}`,
      });
    }

    // Soft delete by setting is_active to false in database
    const result = await query(
      "UPDATE users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING id, name, email, is_active, updated_at",
      [id]
    );

    res.json({
      message: "Employee deactivated successfully",
      employee: result.rows[0],
      note: "Employee has been soft-deleted (deactivated)",
    });
  } catch (error) {
    console.error("Delete employee error:", error);
    res.status(500).json({
      error: "Failed to delete employee",
      message: "An error occurred while deleting the employee",
    });
  }
});

// GET /api/employees/stats/summary - Get employee statistics
router.get("/stats/summary", async (req, res) => {
  try {
    const activeEmployees = employees.filter((emp) => emp.isActive);

    const stats = {
      total: employees.length,
      active: activeEmployees.length,
      inactive: employees.length - activeEmployees.length,
      byRole: {},
      byTeam: {},
    };

    // Calculate statistics with auto-error recovery
    try {
      activeEmployees.forEach((emp) => {
        // By role
        stats.byRole[emp.role] = (stats.byRole[emp.role] || 0) + 1;

        // By team
        stats.byTeam[emp.team] = (stats.byTeam[emp.team] || 0) + 1;
      });

      // Additional statistics can be calculated here
    } catch (calcError) {
      console.warn("Statistics calculation error:", calcError);
      // Return basic stats if calculation fails
      stats.byRole = { error: "Calculation failed" };
      stats.byTeam = { error: "Calculation failed" };
    }

    res.json({
      statistics: stats,
      generatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Get stats error:", error);
    res.status(500).json({
      error: "Failed to generate statistics",
      message: "An error occurred while calculating employee statistics",
    });
  }
});

module.exports = router;
