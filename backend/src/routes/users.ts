import express from 'express';
import { authenticateToken } from './auth';

const router = express.Router();

// Mock database
let users: any[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'manager',
    team_id: null,
    manager_id: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Get all users (for managers)
router.get('/', authenticateToken, (req, res) => {
  try {
    const userRole = (req as any).user.role;
    
    if (!['manager', 'team_leader', 'billing_team'].includes(userRole)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const usersWithoutPasswords = users.map(({ password_hash, ...user }) => user);
    res.json({ users: usersWithoutPasswords });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Get user by ID
router.get('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const currentUserId = (req as any).user.userId;
    const userRole = (req as any).user.role;

    // Users can only view their own profile unless they're managers
    if (id !== currentUserId && !['manager', 'team_leader'].includes(userRole)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const user = users.find(u => u.id === id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const { password_hash, ...userWithoutPassword } = user;
    res.json({ user: userWithoutPassword });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;
