const express = require("express");
const { authenticateToken } = require("./auth");

const router = express.Router();

// Database-backed dashboard stats - no sample data
const getDashboardStats = async (userRole, userId) => {
  // Initialize empty data structure
  const mockDailyCounts = [];

  const today = new Date().toISOString().split("T")[0];

  // Filter based on user role
  let relevantCounts = mockDailyCounts;
  if (userRole === "employee") {
    relevantCounts = mockDailyCounts.filter(
      (count) => count.user_id === userId
    );
  }

  const totalCounts = relevantCounts.reduce(
    (sum, count) => sum + count.count,
    0
  );
  const todayCounts = relevantCounts
    .filter((count) => count.date === today)
    .reduce((sum, count) => sum + count.count, 0);

  // Institute-wise counts
  const instituteCounts = relevantCounts.reduce((acc, count) => {
    const existingInstitute = acc.find(
      (i) => i.institute_id === count.institute_id
    );
    if (existingInstitute) {
      existingInstitute.count += count.count;
    } else {
      acc.push({
        institute_id: count.institute_id,
        institute_name: count.institute_name,
        count: count.count,
      });
    }
    return acc;
  }, []);

  // Product-wise counts
  const productCounts = relevantCounts.reduce((acc, count) => {
    const existingProduct = acc.find((p) => p.product_id === count.product_id);
    if (existingProduct) {
      existingProduct.count += count.count;
    } else {
      acc.push({
        product_id: count.product_id,
        product_name: count.product_name,
        count: count.count,
      });
    }
    return acc;
  }, []);

  // Institute-Product combination counts
  const instituteProductCounts = relevantCounts.reduce((acc, count) => {
    const key = `${count.institute_id}-${count.product_id}`;
    const existing = acc.find((ip) => ip.key === key);
    if (existing) {
      existing.count += count.count;
      existing.services.push({
        service_id: count.service_id,
        service_name: count.service_name,
        count: count.count,
        location_type: count.location_type,
        verifier_name: count.verifier_name,
      });
    } else {
      acc.push({
        key,
        institute_id: count.institute_id,
        institute_name: count.institute_name,
        product_id: count.product_id,
        product_name: count.product_name,
        count: count.count,
        services: [
          {
            service_id: count.service_id,
            service_name: count.service_name,
            count: count.count,
            location_type: count.location_type,
            verifier_name: count.verifier_name,
          },
        ],
      });
    }
    return acc;
  }, []);

  // Team-wise counts
  const teamCounts = relevantCounts.reduce((acc, count) => {
    const existingTeam = acc.find((t) => t.team_id === count.team_id);
    if (existingTeam) {
      existingTeam.count += count.count;
    } else {
      acc.push({
        team_id: count.team_id,
        team_name: `Team ${count.team_id}`,
        count: count.count,
      });
    }
    return acc;
  }, []);

  // Location-wise counts
  const locationCounts = relevantCounts.reduce(
    (acc, count) => {
      acc[count.location_type] = (acc[count.location_type] || 0) + count.count;
      return acc;
    },
    { local: 0, ogl: 0, outstation: 0 }
  );

  return {
    totalCounts,
    todayCounts,
    instituteCounts,
    productCounts,
    instituteProductCounts,
    teamCounts,
    locationCounts,
  };
};

// Get dashboard statistics
router.get("/stats", authenticateToken, (req, res) => {
  try {
    const userId = req.user.userId;
    const userRole = req.user.role;

    const stats = getDashboardStats(userRole, userId);
    res.json(stats);
  } catch (error) {
    console.error("Get dashboard stats error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Get recent activities
router.get("/activities", authenticateToken, (req, res) => {
  try {
    const userId = req.user.userId;
    const userRole = req.user.role;

    // No sample activities - fetch from database
    const mockActivities = [];

    // Filter activities based on user role
    let activities = mockActivities;
    if (userRole === "employee") {
      activities = mockActivities.filter(
        (activity) => activity.user_name === "Current User"
      );
    }

    res.json({ activities });
  } catch (error) {
    console.error("Get dashboard activities error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Get employee assignments (institutes and products assigned to employee)
router.get("/assignments", authenticateToken, (req, res) => {
  try {
    const userId = req.user.userId;
    const userRole = req.user.role;

    // No sample assignments - fetch from database
    const mockAssignments = [];

    // For employees, return only their assignments
    if (userRole === "employee") {
      const userAssignments = mockAssignments.find((a) => a.user_id === userId);
      if (!userAssignments) {
        return res.json({
          institutes: [],
          products: [],
          services: [],
          message: "No assignments found for this employee",
        });
      }
      return res.json(userAssignments);
    }

    // For managers and other roles, return all assignments
    res.json({ assignments: mockAssignments });
  } catch (error) {
    console.error("Get employee assignments error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

module.exports = router;
