const express = require("express");
const { body, validationResult } = require("express-validator");
const errorRecoveryManager = require("../middleware/errorRecovery");

const router = express.Router();

// In-memory storage for demo (in production, use a database)
let institutes = [];

let locations = [];

let products = [];

let services = [];

let rates = [];

let nextInstituteId = 1;
let nextLocationId = 1;
let nextProductId = 1;
let nextServiceId = 1;
let nextRateId = 1;

// Simplified validation rules
const instituteValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Institute name must be between 2 and 100 characters")
    .customSanitizer((value) => {
      return value.replace(/\b\w/g, (char) => char.toUpperCase());
    }),
];

const locationValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Location name must be between 2 and 100 characters"),
];

const productValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Product name must be between 2 and 100 characters"),
];

const serviceValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Service name must be between 2 and 100 characters"),
];

const rateValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Rate name must be between 2 and 100 characters"),
];

// GET /api/masters/institutes - Get all institutes
router.get("/institutes", async (req, res) => {
  try {
    const { search = "", isActive = "" } = req.query;

    let filteredInstitutes = [...institutes];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredInstitutes = filteredInstitutes.filter(
        (inst) =>
          inst.name.toLowerCase().includes(searchLower) ||
          inst.code.toLowerCase().includes(searchLower) ||
          inst.location.toLowerCase().includes(searchLower)
      );
    }

    if (isActive !== "") {
      const activeFilter = isActive === "true";
      filteredInstitutes = filteredInstitutes.filter(
        (inst) => inst.isActive === activeFilter
      );
    }

    res.json({
      institutes: filteredInstitutes,
      total: filteredInstitutes.length,
    });
  } catch (error) {
    console.error("Get institutes error:", error);
    res.status(500).json({
      error: "Failed to retrieve institutes",
      message: "An error occurred while fetching institute data",
    });
  }
});

// POST /api/masters/institutes - Create new institute
router.post("/institutes", instituteValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, code, description = "", isActive = true } = req.body;

    // Check for duplicate code
    const existingInstitute = institutes.find((inst) => inst.code === code);
    if (existingInstitute) {
      return res.status(409).json({
        error: "Institute code already exists",
        message: `Institute with code ${code} already exists`,
      });
    }

    const newInstitute = {
      id: nextInstituteId.toString(),
      name,
      code,
      location: "Default Location",
      description,
      isActive,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    institutes.push(newInstitute);
    nextInstituteId++;

    res.status(201).json({
      message: "Institute created successfully",
      institute: newInstitute,
    });
  } catch (error) {
    console.error("Create institute error:", error);
    res.status(500).json({
      error: "Failed to create institute",
      message: "An error occurred while creating the institute",
    });
  }
});

// PUT /api/masters/institutes/:id - Update institute
router.put("/institutes/:id", instituteValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { id } = req.params;
    const { name, isActive = true } = req.body;

    // Find the institute to update
    const instituteIndex = institutes.findIndex((inst) => inst.id === id);
    if (instituteIndex === -1) {
      return res.status(404).json({
        error: "Institute not found",
        message: `Institute with ID ${id} does not exist`,
      });
    }

    // Update the institute
    const updatedInstitute = {
      ...institutes[instituteIndex],
      name,
      isActive,
      updatedAt: new Date().toISOString(),
    };

    institutes[instituteIndex] = updatedInstitute;

    res.json({
      message: "Institute updated successfully",
      institute: updatedInstitute,
    });
  } catch (error) {
    console.error("Update institute error:", error);
    res.status(500).json({
      error: "Failed to update institute",
      message: "An error occurred while updating the institute",
    });
  }
});

// GET /api/masters/locations - Get all locations
router.get("/locations", async (req, res) => {
  try {
    const { search = "", type = "", isActive = "" } = req.query;

    let filteredLocations = [...locations];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredLocations = filteredLocations.filter(
        (loc) =>
          loc.name.toLowerCase().includes(searchLower) ||
          loc.code.toLowerCase().includes(searchLower) ||
          loc.description.toLowerCase().includes(searchLower)
      );
    }

    if (type) {
      filteredLocations = filteredLocations.filter((loc) => loc.type === type);
    }

    if (isActive !== "") {
      const activeFilter = isActive === "true";
      filteredLocations = filteredLocations.filter(
        (loc) => loc.isActive === activeFilter
      );
    }

    res.json({
      locations: filteredLocations,
      total: filteredLocations.length,
    });
  } catch (error) {
    console.error("Get locations error:", error);
    res.status(500).json({
      error: "Failed to retrieve locations",
      message: "An error occurred while fetching location data",
    });
  }
});

// POST /api/masters/locations - Create new location
router.post("/locations", locationValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, code, type = "verification", isActive = true } = req.body;

    // Check for duplicate code
    const existingLocation = locations.find((loc) => loc.code === code);
    if (existingLocation) {
      return res.status(409).json({
        error: "Location code already exists",
        message: `Location with code ${code} already exists`,
      });
    }

    const newLocation = {
      id: nextLocationId.toString(),
      name,
      code,
      type,
      description: "",
      isActive,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    locations.push(newLocation);
    nextLocationId++;

    res.status(201).json({
      message: "Location created successfully",
      location: newLocation,
    });
  } catch (error) {
    console.error("Create location error:", error);
    res.status(500).json({
      error: "Failed to create location",
      message: "An error occurred while creating the location",
    });
  }
});

// PUT /api/masters/locations/:id - Update location
router.put("/locations/:id", locationValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { id } = req.params;
    const { name, isActive = true } = req.body;

    // Find the location to update
    const locationIndex = locations.findIndex((loc) => loc.id === id);
    if (locationIndex === -1) {
      return res.status(404).json({
        error: "Location not found",
        message: `Location with ID ${id} does not exist`,
      });
    }

    // Update the location
    const updatedLocation = {
      ...locations[locationIndex],
      name,
      isActive,
      updatedAt: new Date().toISOString(),
    };

    locations[locationIndex] = updatedLocation;

    res.json({
      message: "Location updated successfully",
      location: updatedLocation,
    });
  } catch (error) {
    console.error("Update location error:", error);
    res.status(500).json({
      error: "Failed to update location",
      message: "An error occurred while updating the location",
    });
  }
});

// GET /api/masters/products - Get all products
router.get("/products", async (req, res) => {
  try {
    const { search = "", category = "", isActive = "" } = req.query;

    let filteredProducts = [...products];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredProducts = filteredProducts.filter(
        (prod) =>
          prod.name.toLowerCase().includes(searchLower) ||
          prod.code.toLowerCase().includes(searchLower) ||
          prod.description.toLowerCase().includes(searchLower)
      );
    }

    if (category) {
      filteredProducts = filteredProducts.filter(
        (prod) => prod.category === category
      );
    }

    if (isActive !== "") {
      const activeFilter = isActive === "true";
      filteredProducts = filteredProducts.filter(
        (prod) => prod.isActive === activeFilter
      );
    }

    res.json({
      products: filteredProducts,
      total: filteredProducts.length,
    });
  } catch (error) {
    console.error("Get products error:", error);
    res.status(500).json({
      error: "Failed to retrieve products",
      message: "An error occurred while fetching product data",
    });
  }
});

// POST /api/masters/products - Create new product
router.post("/products", productValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, code, isActive = true } = req.body;

    // Check for duplicate code
    const existingProduct = products.find((prod) => prod.code === code);
    if (existingProduct) {
      return res.status(409).json({
        error: "Product code already exists",
        message: `Product with code ${code} already exists`,
      });
    }

    const newProduct = {
      id: nextProductId.toString(),
      name,
      code,
      category: "General",
      description: "",
      isActive,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    products.push(newProduct);
    nextProductId++;

    res.status(201).json({
      message: "Product created successfully",
      product: newProduct,
    });
  } catch (error) {
    console.error("Create product error:", error);
    res.status(500).json({
      error: "Failed to create product",
      message: "An error occurred while creating the product",
    });
  }
});

// PUT /api/masters/products/:id - Update product
router.put("/products/:id", productValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { id } = req.params;
    const { name, isActive = true } = req.body;

    // Find the product to update
    const productIndex = products.findIndex((prod) => prod.id === id);
    if (productIndex === -1) {
      return res.status(404).json({
        error: "Product not found",
        message: `Product with ID ${id} does not exist`,
      });
    }

    // Update the product
    const updatedProduct = {
      ...products[productIndex],
      name,
      isActive,
      updatedAt: new Date().toISOString(),
    };

    products[productIndex] = updatedProduct;

    res.json({
      message: "Product updated successfully",
      product: updatedProduct,
    });
  } catch (error) {
    console.error("Update product error:", error);
    res.status(500).json({
      error: "Failed to update product",
      message: "An error occurred while updating the product",
    });
  }
});

// GET /api/masters/services - Get all services
router.get("/services", async (req, res) => {
  try {
    const { search = "", category = "", isActive = "" } = req.query;

    let filteredServices = [...services];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredServices = filteredServices.filter(
        (serv) =>
          serv.name.toLowerCase().includes(searchLower) ||
          serv.code.toLowerCase().includes(searchLower) ||
          serv.description.toLowerCase().includes(searchLower)
      );
    }

    if (category) {
      filteredServices = filteredServices.filter(
        (serv) => serv.category === category
      );
    }

    if (isActive !== "") {
      const activeFilter = isActive === "true";
      filteredServices = filteredServices.filter(
        (serv) => serv.isActive === activeFilter
      );
    }

    res.json({
      services: filteredServices,
      total: filteredServices.length,
    });
  } catch (error) {
    console.error("Get services error:", error);
    res.status(500).json({
      error: "Failed to retrieve services",
      message: "An error occurred while fetching service data",
    });
  }
});

// POST /api/masters/services - Create new service
router.post("/services", serviceValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, code, isActive = true } = req.body;

    // Check for duplicate code
    const existingService = services.find((serv) => serv.code === code);
    if (existingService) {
      return res.status(409).json({
        error: "Service code already exists",
        message: `Service with code ${code} already exists`,
      });
    }

    const newService = {
      id: nextServiceId.toString(),
      name,
      code,
      category: "General",
      description: "",
      isActive,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    services.push(newService);
    nextServiceId++;

    res.status(201).json({
      message: "Service created successfully",
      service: newService,
    });
  } catch (error) {
    console.error("Create service error:", error);
    res.status(500).json({
      error: "Failed to create service",
      message: "An error occurred while creating the service",
    });
  }
});

// PUT /api/masters/services/:id - Update service
router.put("/services/:id", serviceValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { id } = req.params;
    const { name, isActive = true } = req.body;

    // Find the service to update
    const serviceIndex = services.findIndex((serv) => serv.id === id);
    if (serviceIndex === -1) {
      return res.status(404).json({
        error: "Service not found",
        message: `Service with ID ${id} does not exist`,
      });
    }

    // Update the service
    const updatedService = {
      ...services[serviceIndex],
      name,
      isActive,
      updatedAt: new Date().toISOString(),
    };

    services[serviceIndex] = updatedService;

    res.json({
      message: "Service updated successfully",
      service: updatedService,
    });
  } catch (error) {
    console.error("Update service error:", error);
    res.status(500).json({
      error: "Failed to update service",
      message: "An error occurred while updating the service",
    });
  }
});

// GET /api/masters/rates - Get all rates
router.get("/rates", async (req, res) => {
  try {
    const { role = "", level = "", location = "", isActive = "" } = req.query;

    let filteredRates = [...rates];

    if (role) {
      filteredRates = filteredRates.filter((rate) => rate.role === role);
    }

    if (level) {
      filteredRates = filteredRates.filter((rate) => rate.level === level);
    }

    if (location) {
      filteredRates = filteredRates.filter(
        (rate) => rate.location === location
      );
    }

    if (isActive !== "") {
      const activeFilter = isActive === "true";
      filteredRates = filteredRates.filter(
        (rate) => rate.isActive === activeFilter
      );
    }

    res.json({
      rates: filteredRates,
      total: filteredRates.length,
    });
  } catch (error) {
    console.error("Get rates error:", error);
    res.status(500).json({
      error: "Failed to retrieve rates",
      message: "An error occurred while fetching rate data",
    });
  }
});

// POST /api/masters/rates - Create new rate
router.post("/rates", rateValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { name, code, isActive = true } = req.body;

    // Check for duplicate code
    const existingRate = rates.find((rate) => rate.code === code);
    if (existingRate) {
      return res.status(409).json({
        error: "Rate code already exists",
        message: `Rate with code ${code} already exists`,
      });
    }

    const newRate = {
      id: nextRateId.toString(),
      name,
      code,
      role: "general",
      level: "standard",
      location: "local",
      dailyRate: 500,
      hourlyRate: 62.5,
      effectiveFrom: new Date().toISOString().split("T")[0],
      effectiveTo: null,
      isActive,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    rates.push(newRate);
    nextRateId++;

    res.status(201).json({
      message: "Rate created successfully",
      rate: newRate,
    });
  } catch (error) {
    console.error("Create rate error:", error);
    res.status(500).json({
      error: "Failed to create rate",
      message: "An error occurred while creating the rate",
    });
  }
});

// PUT /api/masters/rates/:id - Update rate
router.put("/rates/:id", rateValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { id } = req.params;
    const { name, isActive = true } = req.body;

    // Find the rate to update
    const rateIndex = rates.findIndex((rate) => rate.id === id);
    if (rateIndex === -1) {
      return res.status(404).json({
        error: "Rate not found",
        message: `Rate with ID ${id} does not exist`,
      });
    }

    // Update the rate
    const updatedRate = {
      ...rates[rateIndex],
      name,
      isActive,
      updatedAt: new Date().toISOString(),
    };

    rates[rateIndex] = updatedRate;

    res.json({
      message: "Rate updated successfully",
      rate: updatedRate,
    });
  } catch (error) {
    console.error("Update rate error:", error);
    res.status(500).json({
      error: "Failed to update rate",
      message: "An error occurred while updating the rate",
    });
  }
});

// GET /api/masters/rate-lookup - Get rate for specific role/level/location
router.get("/rate-lookup", async (req, res) => {
  try {
    const {
      role,
      level,
      location,
      date = new Date().toISOString().split("T")[0],
    } = req.query;

    if (!role || !level || !location) {
      return res.status(400).json({
        error: "Missing parameters",
        message: "Role, level, and location are required",
      });
    }

    // Find active rate for the specified criteria and date
    const applicableRate = rates.find(
      (rate) =>
        rate.role === role &&
        rate.level === level &&
        rate.location === location &&
        rate.isActive === true &&
        new Date(rate.effectiveFrom) <= new Date(date) &&
        (!rate.effectiveTo || new Date(rate.effectiveTo) >= new Date(date))
    );

    if (!applicableRate) {
      return res.status(404).json({
        error: "Rate not found",
        message: `No active rate found for ${role} ${level} at ${location} for date ${date}`,
      });
    }

    res.json({
      rate: applicableRate,
      message: "Rate found successfully",
    });
  } catch (error) {
    console.error("Rate lookup error:", error);
    res.status(500).json({
      error: "Failed to lookup rate",
      message: "An error occurred while looking up the rate",
    });
  }
});

// POST /api/masters/import/acs-los - Import data from AllCheckServices LOS
router.post("/import/acs-los", async (req, res) => {
  try {
    const { apiUrl, apiKey, dataType, credentials } = req.body;

    if (!apiUrl || !dataType) {
      return res.status(400).json({
        error: "Missing required parameters: apiUrl, dataType",
      });
    }

    const headers = {
      "Content-Type": "application/json",
      "User-Agent": "ACS-Billing-System/1.0",
    };

    if (apiKey) {
      headers["Authorization"] = `Bearer ${apiKey}`;
    }

    if (credentials) {
      headers["Authorization"] = `Basic ${Buffer.from(
        `${credentials.username}:${credentials.password}`
      ).toString("base64")}`;
    }

    // Fetch data from AllCheckServices LOS
    const response = await fetch(apiUrl, {
      method: "GET",
      headers,
      timeout: 30000,
    });

    if (!response.ok) {
      throw new Error(
        `HTTP error! status: ${response.status} - ${response.statusText}`
      );
    }

    const data = await response.json();
    let importedCount = 0;
    let errors = [];

    // Process different data types
    switch (dataType) {
      case "institutes":
        if (data.institutes || Array.isArray(data)) {
          const instituteData = data.institutes || data;
          for (const item of instituteData) {
            try {
              const institute = {
                id: nextInstituteId.toString(),
                name:
                  item.name ||
                  item.institute_name ||
                  item.instituteName ||
                  item.client_name,
                code: (
                  item.code ||
                  item.institute_code ||
                  item.instituteCode ||
                  item.client_code ||
                  ""
                ).toUpperCase(),
                location:
                  item.location ||
                  item.city ||
                  item.address ||
                  item.state ||
                  "Unknown",
                isActive: item.isActive !== false && item.status !== "inactive",
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              };

              // Auto-generate code if missing
              if (!institute.code && institute.name) {
                institute.code =
                  institute.name.substring(0, 3).toUpperCase() +
                  nextInstituteId;
              }

              // Validate required fields
              if (!institute.name || !institute.code) {
                errors.push(
                  `Skipped institute: Missing name or code - ${JSON.stringify(
                    item
                  )}`
                );
                continue;
              }

              // Check for duplicates
              const exists = institutes.find((i) => i.code === institute.code);
              if (exists) {
                errors.push(
                  `Skipped institute: Code already exists - ${institute.code}`
                );
                continue;
              }

              institutes.push(institute);
              nextInstituteId++;
              importedCount++;
            } catch (error) {
              errors.push(`Error processing institute: ${error.message}`);
            }
          }
        }
        break;

      case "products":
        if (data.products || Array.isArray(data)) {
          const productData = data.products || data;
          for (const item of productData) {
            try {
              const product = {
                id: nextProductId.toString(),
                name:
                  item.name ||
                  item.product_name ||
                  item.productName ||
                  item.service_name,
                code: (
                  item.code ||
                  item.product_code ||
                  item.productCode ||
                  item.service_code ||
                  ""
                ).toUpperCase(),
                category:
                  item.category ||
                  item.product_category ||
                  item.type ||
                  "General",
                description:
                  item.description || item.desc || item.details || "",
                isActive: item.isActive !== false && item.status !== "inactive",
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              };

              // Auto-generate code if missing
              if (!product.code && product.name) {
                product.code =
                  product.name.substring(0, 3).toUpperCase() + nextProductId;
              }

              // Validate required fields
              if (!product.name || !product.code) {
                errors.push(
                  `Skipped product: Missing name or code - ${JSON.stringify(
                    item
                  )}`
                );
                continue;
              }

              // Check for duplicates
              const exists = products.find((p) => p.code === product.code);
              if (exists) {
                errors.push(
                  `Skipped product: Code already exists - ${product.code}`
                );
                continue;
              }

              products.push(product);
              nextProductId++;
              importedCount++;
            } catch (error) {
              errors.push(`Error processing product: ${error.message}`);
            }
          }
        }
        break;

      case "services":
        if (data.services || Array.isArray(data)) {
          const serviceData = data.services || data;
          for (const item of serviceData) {
            try {
              const service = {
                id: nextServiceId.toString(),
                name:
                  item.name ||
                  item.service_name ||
                  item.serviceName ||
                  item.check_name,
                code: (
                  item.code ||
                  item.service_code ||
                  item.serviceCode ||
                  item.check_code ||
                  ""
                ).toUpperCase(),
                category:
                  item.category ||
                  item.service_category ||
                  item.check_category ||
                  "General",
                description:
                  item.description || item.desc || item.details || "",
                isActive: item.isActive !== false && item.status !== "inactive",
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              };

              // Auto-generate code if missing
              if (!service.code && service.name) {
                service.code =
                  service.name.substring(0, 3).toUpperCase() + nextServiceId;
              }

              // Validate required fields
              if (!service.name || !service.code) {
                errors.push(
                  `Skipped service: Missing name or code - ${JSON.stringify(
                    item
                  )}`
                );
                continue;
              }

              // Check for duplicates
              const exists = services.find((s) => s.code === service.code);
              if (exists) {
                errors.push(
                  `Skipped service: Code already exists - ${service.code}`
                );
                continue;
              }

              services.push(service);
              nextServiceId++;
              importedCount++;
            } catch (error) {
              errors.push(`Error processing service: ${error.message}`);
            }
          }
        }
        break;

      case "locations":
        if (data.locations || Array.isArray(data)) {
          const locationData = data.locations || data;
          for (const item of locationData) {
            try {
              const location = {
                id: nextLocationId.toString(),
                name:
                  item.name ||
                  item.location_name ||
                  item.locationName ||
                  item.place_name,
                code: (
                  item.code ||
                  item.location_code ||
                  item.locationCode ||
                  item.place_code ||
                  ""
                ).toUpperCase(),
                type:
                  item.type ||
                  item.location_type ||
                  item.verificationType ||
                  "verification",
                description:
                  item.description || item.desc || item.details || "",
                isActive: item.isActive !== false && item.status !== "inactive",
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              };

              // Auto-generate code if missing
              if (!location.code && location.name) {
                location.code =
                  location.name.substring(0, 3).toUpperCase() + nextLocationId;
              }

              // Validate required fields
              if (!location.name || !location.code) {
                errors.push(
                  `Skipped location: Missing name or code - ${JSON.stringify(
                    item
                  )}`
                );
                continue;
              }

              // Check for duplicates
              const exists = locations.find((l) => l.code === location.code);
              if (exists) {
                errors.push(
                  `Skipped location: Code already exists - ${location.code}`
                );
                continue;
              }

              locations.push(location);
              nextLocationId++;
              importedCount++;
            } catch (error) {
              errors.push(`Error processing location: ${error.message}`);
            }
          }
        }
        break;

      default:
        return res.status(400).json({
          error:
            "Invalid dataType. Must be: institutes, locations, products, or services",
        });
    }

    res.json({
      success: true,
      message: `Successfully imported ${importedCount} ${dataType}`,
      imported: importedCount,
      errors: errors.length > 0 ? errors : undefined,
      summary: {
        total_institutes: institutes.length,
        total_locations: locations.length,
        total_products: products.length,
        total_services: services.length,
      },
    });
  } catch (error) {
    console.error("Import error:", error);
    res.status(500).json({
      error: "Failed to import data from AllCheckServices LOS",
      details: error.message,
    });
  }
});

// POST /api/masters/import/file - Import data from uploaded file
router.post("/import/file", async (req, res) => {
  try {
    const { data, dataType, format } = req.body;

    if (!data || !dataType) {
      return res.status(400).json({
        error: "Missing required parameters: data, dataType",
      });
    }

    let parsedData;

    // Parse data based on format
    if (format === "csv") {
      // Simple CSV parsing (you might want to use a proper CSV parser)
      const lines = data.split("\n");
      const headers = lines[0].split(",").map((h) => h.trim());
      parsedData = lines
        .slice(1)
        .map((line) => {
          const values = line.split(",").map((v) => v.trim());
          const obj = {};
          headers.forEach((header, index) => {
            obj[header] = values[index] || "";
          });
          return obj;
        })
        .filter((obj) => Object.values(obj).some((v) => v)); // Remove empty rows
    } else {
      // Assume JSON format
      parsedData = Array.isArray(data) ? data : [data];
    }

    let importedCount = 0;
    let errors = [];

    // Process the data similar to ACS LOS import
    for (const item of parsedData) {
      try {
        let newItem;

        switch (dataType) {
          case "institutes":
            newItem = {
              id: nextInstituteId.toString(),
              name: item.name || item.institute_name || item.instituteName,
              code: (
                item.code ||
                item.institute_code ||
                item.instituteCode ||
                ""
              ).toUpperCase(),
              location: item.location || item.city || item.address || "Unknown",
              isActive: item.isActive !== false,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };

            if (!newItem.code && newItem.name) {
              newItem.code =
                newItem.name.substring(0, 3).toUpperCase() + nextInstituteId;
            }

            if (!newItem.name || !newItem.code) {
              errors.push(`Skipped institute: Missing name or code`);
              continue;
            }

            const existsInst = institutes.find((i) => i.code === newItem.code);
            if (existsInst) {
              errors.push(
                `Skipped institute: Code already exists - ${newItem.code}`
              );
              continue;
            }

            institutes.push(newItem);
            nextInstituteId++;
            break;

          case "products":
            newItem = {
              id: nextProductId.toString(),
              name: item.name || item.product_name || item.productName,
              code: (
                item.code ||
                item.product_code ||
                item.productCode ||
                ""
              ).toUpperCase(),
              category: item.category || item.product_category || "General",
              description: item.description || item.desc || "",
              isActive: item.isActive !== false,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };

            if (!newItem.code && newItem.name) {
              newItem.code =
                newItem.name.substring(0, 3).toUpperCase() + nextProductId;
            }

            if (!newItem.name || !newItem.code) {
              errors.push(`Skipped product: Missing name or code`);
              continue;
            }

            const existsProd = products.find((p) => p.code === newItem.code);
            if (existsProd) {
              errors.push(
                `Skipped product: Code already exists - ${newItem.code}`
              );
              continue;
            }

            products.push(newItem);
            nextProductId++;
            break;

          case "services":
            newItem = {
              id: nextServiceId.toString(),
              name: item.name || item.service_name || item.serviceName,
              code: (
                item.code ||
                item.service_code ||
                item.serviceCode ||
                ""
              ).toUpperCase(),
              category: item.category || item.service_category || "General",
              description: item.description || item.desc || "",
              isActive: item.isActive !== false,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };

            if (!newItem.code && newItem.name) {
              newItem.code =
                newItem.name.substring(0, 3).toUpperCase() + nextServiceId;
            }

            if (!newItem.name || !newItem.code) {
              errors.push(`Skipped service: Missing name or code`);
              continue;
            }

            const existsServ = services.find((s) => s.code === newItem.code);
            if (existsServ) {
              errors.push(
                `Skipped service: Code already exists - ${newItem.code}`
              );
              continue;
            }

            services.push(newItem);
            nextServiceId++;
            break;

          case "locations":
            newItem = {
              id: nextLocationId.toString(),
              name: item.name || item.location_name || item.locationName,
              code: (
                item.code ||
                item.location_code ||
                item.locationCode ||
                ""
              ).toUpperCase(),
              type: item.type || item.location_type || "verification",
              description: item.description || item.desc || "",
              isActive: item.isActive !== false,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };

            if (!newItem.code && newItem.name) {
              newItem.code =
                newItem.name.substring(0, 3).toUpperCase() + nextLocationId;
            }

            if (!newItem.name || !newItem.code) {
              errors.push(`Skipped location: Missing name or code`);
              continue;
            }

            const existsLoc = locations.find((l) => l.code === newItem.code);
            if (existsLoc) {
              errors.push(
                `Skipped location: Code already exists - ${newItem.code}`
              );
              continue;
            }

            locations.push(newItem);
            nextLocationId++;
            break;

          default:
            errors.push(`Invalid dataType: ${dataType}`);
            continue;
        }

        importedCount++;
      } catch (error) {
        errors.push(`Error processing item: ${error.message}`);
      }
    }

    res.json({
      success: true,
      message: `Successfully imported ${importedCount} ${dataType} from file`,
      imported: importedCount,
      errors: errors.length > 0 ? errors : undefined,
      summary: {
        total_institutes: institutes.length,
        total_locations: locations.length,
        total_products: products.length,
        total_services: services.length,
      },
    });
  } catch (error) {
    console.error("File import error:", error);
    res.status(500).json({
      error: "Failed to import data from file",
      details: error.message,
    });
  }
});

module.exports = router;
