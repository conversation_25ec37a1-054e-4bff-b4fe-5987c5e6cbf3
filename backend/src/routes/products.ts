import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticateToken } from './auth';

const router = express.Router();

// Mock database
let products: any[] = [
  {
    id: '1',
    name: 'Product A',
    description: 'Description for Product A',
    base_rate: 100.00,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '2',
    name: 'Product B',
    description: 'Description for Product B',
    base_rate: 150.00,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Get all products
router.get('/', authenticateToken, (req, res) => {
  try {
    res.json({ products });
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Create product (managers only)
router.post('/', authenticateToken, [
  body('name').trim().isLength({ min: 2 }),
  body('description').trim().isLength({ min: 5 }),
  body('base_rate').isFloat({ min: 0 })
], (req, res) => {
  try {
    const userRole = (req as any).user.role;
    
    if (!['manager', 'billing_team'].includes(userRole)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, description, base_rate } = req.body;

    // Check if product name already exists
    const existingProduct = products.find(p => p.name.toLowerCase() === name.toLowerCase());
    if (existingProduct) {
      return res.status(400).json({ message: 'Product name already exists' });
    }

    const newProduct = {
      id: (products.length + 1).toString(),
      name,
      description,
      base_rate: parseFloat(base_rate),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    products.push(newProduct);
    res.status(201).json({ product: newProduct });
  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;
