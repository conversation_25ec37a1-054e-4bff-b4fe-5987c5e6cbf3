import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticateToken } from './auth';

const router = express.Router();

// Mock database
let institutes: any[] = [
  {
    id: '1',
    name: 'Institute A',
    address: '123 Main St, City',
    billing_format: 'Standard Format',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '2',
    name: 'Institute B',
    address: '456 Oak Ave, Town',
    billing_format: 'Premium Format',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Get all institutes
router.get('/', authenticateToken, (req, res) => {
  try {
    res.json({ institutes });
  } catch (error) {
    console.error('Get institutes error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Create institute (managers only)
router.post('/', authenticateToken, [
  body('name').trim().isLength({ min: 2 }),
  body('address').trim().isLength({ min: 5 }),
  body('billing_format').trim().isLength({ min: 2 })
], (req, res) => {
  try {
    const userRole = (req as any).user.role;
    
    if (!['manager', 'billing_team'].includes(userRole)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, address, billing_format } = req.body;

    // Check if institute name already exists
    const existingInstitute = institutes.find(i => i.name.toLowerCase() === name.toLowerCase());
    if (existingInstitute) {
      return res.status(400).json({ message: 'Institute name already exists' });
    }

    const newInstitute = {
      id: (institutes.length + 1).toString(),
      name,
      address,
      billing_format,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    institutes.push(newInstitute);
    res.status(201).json({ institute: newInstitute });
  } catch (error) {
    console.error('Create institute error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;
