const express = require("express");
const { body, validationResult } = require("express-validator");
const errorRecoveryManager = require("../middleware/errorRecovery");

const router = express.Router();

// In-memory storage for demo (in production, use a database)
let dailyCounts = [];

let nextId = 1;

// Validation rules with auto-correction
const dailyCountValidation = [
  body("employeeId").notEmpty().withMessage("Employee ID is required"),

  body("fieldEmployeeId")
    .notEmpty()
    .withMessage("Field Employee ID is required"),

  body("date")
    .isISO8601()
    .withMessage("Please provide a valid date in YYYY-MM-DD format")
    .customSanitizer((value) => {
      // Auto-fix: Ensure date is in correct format
      return new Date(value).toISOString().split("T")[0];
    }),

  body("institute")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Institute name must be between 2 and 100 characters"),

  body("product")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Product name must be between 2 and 100 characters"),

  body("location")
    .isIn(["local", "outstation", "ogl"])
    .withMessage("Location must be local, outstation, or ogl"),

  body("count")
    .isInt({ min: 0, max: 1000 })
    .withMessage("Count must be between 0 and 1000")
    .customSanitizer((value) => {
      // Auto-fix: Ensure it's an integer
      return parseInt(value) || 0;
    }),

  body("notes")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Notes must not exceed 500 characters"),
];

// GET /api/daily-counts - Get all daily counts with filtering
router.get("/", async (req, res) => {
  try {
    // Check circuit breaker
    if (!errorRecoveryManager.checkCircuitBreaker("api")) {
      return res.status(503).json({
        error: "Service temporarily unavailable",
        message: "API service is recovering. Please try again later.",
      });
    }

    const {
      page = 1,
      limit = 10,
      employeeId = "",
      date = "",
      dateFrom = "",
      dateTo = "",
      location = "",
      institute = "",
      product = "",
    } = req.query;

    let filteredCounts = [...dailyCounts];

    // Apply filters with auto-error recovery
    try {
      if (employeeId) {
        filteredCounts = filteredCounts.filter(
          (count) => count.employeeId === employeeId
        );
      }

      if (date) {
        filteredCounts = filteredCounts.filter((count) => count.date === date);
      }

      if (dateFrom && dateTo) {
        filteredCounts = filteredCounts.filter(
          (count) => count.date >= dateFrom && count.date <= dateTo
        );
      }

      if (location) {
        filteredCounts = filteredCounts.filter(
          (count) => count.location === location
        );
      }

      if (institute) {
        filteredCounts = filteredCounts.filter((count) =>
          count.institute.toLowerCase().includes(institute.toLowerCase())
        );
      }

      if (product) {
        filteredCounts = filteredCounts.filter((count) =>
          count.product.toLowerCase().includes(product.toLowerCase())
        );
      }

      // Sort by date (newest first)
      filteredCounts.sort((a, b) => new Date(b.date) - new Date(a.date));

      // Pagination with auto-correction
      const pageNum = Math.max(1, parseInt(page));
      const limitNum = Math.min(100, Math.max(1, parseInt(limit)));
      const startIndex = (pageNum - 1) * limitNum;
      const endIndex = startIndex + limitNum;

      const paginatedCounts = filteredCounts.slice(startIndex, endIndex);

      // Calculate summary statistics
      const summary = {
        totalRecords: filteredCounts.length,
        totalCount: filteredCounts.reduce((sum, count) => sum + count.count, 0),
        byLocation: {
          local: filteredCounts.filter((c) => c.location === "local").length,
          outstation: filteredCounts.filter((c) => c.location === "outstation")
            .length,
          ogl: filteredCounts.filter((c) => c.location === "ogl").length,
        },
        byInstitute: {},
        byProduct: {},
      };

      // Group by institute and product
      filteredCounts.forEach((count) => {
        summary.byInstitute[count.institute] =
          (summary.byInstitute[count.institute] || 0) + count.count;
        summary.byProduct[count.product] =
          (summary.byProduct[count.product] || 0) + count.count;
      });

      errorRecoveryManager.recordSuccess("api");

      res.json({
        dailyCounts: paginatedCounts,
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(filteredCounts.length / limitNum),
          totalItems: filteredCounts.length,
          itemsPerPage: limitNum,
        },
        summary,
        filters: {
          employeeId,
          date,
          dateFrom,
          dateTo,
          location,
          institute,
          product,
        },
      });
    } catch (filterError) {
      console.warn("Filter error, returning unfiltered results:", filterError);

      res.json({
        dailyCounts: dailyCounts.slice(0, parseInt(limit) || 10),
        pagination: {
          currentPage: 1,
          totalPages: Math.ceil(dailyCounts.length / (parseInt(limit) || 10)),
          totalItems: dailyCounts.length,
          itemsPerPage: parseInt(limit) || 10,
        },
        warning: "Some filters failed, showing default results",
      });
    }
  } catch (error) {
    errorRecoveryManager.recordFailure("api");
    console.error("Get daily counts error:", error);

    res.status(500).json({
      error: "Failed to retrieve daily counts",
      message: "An error occurred while fetching daily count data",
    });
  }
});

// POST /api/daily-counts - Submit new daily count
router.post("/", dailyCountValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
        autoFix: "Some fields have been automatically corrected",
      });
    }

    const {
      employeeId,
      fieldEmployeeId,
      date,
      institute,
      product,
      location,
      count,
      notes = "",
    } = req.body;

    // Check for duplicate entry (same employee, same date, same institute, same product)
    const existingCount = dailyCounts.find(
      (dc) =>
        dc.employeeId === employeeId &&
        dc.date === date &&
        dc.institute === institute &&
        dc.product === product
    );

    if (existingCount) {
      return res.status(409).json({
        error: "Duplicate entry",
        message: `Daily count for employee ${employeeId} on ${date} for ${institute} - ${product} already exists`,
        suggestion: "Update the existing entry instead",
        existingEntry: existingCount,
      });
    }

    // Get employee names (in production, this would be a database join)
    const employeeName = `Employee ${employeeId}`; // Simplified for demo
    const fieldEmployeeName = `Field Employee ${fieldEmployeeId}`; // Simplified for demo

    // Create new daily count entry
    const newDailyCount = {
      id: nextId.toString(),
      employeeId,
      employeeName,
      fieldEmployeeId,
      fieldEmployeeName,
      date,
      institute,
      product,
      location,
      count: parseInt(count),
      submittedBy: employeeId, // In real app, get from auth token
      submittedAt: new Date().toISOString(),
      notes,
    };

    dailyCounts.push(newDailyCount);
    nextId++;

    res.status(201).json({
      message: "Daily count submitted successfully",
      dailyCount: newDailyCount,
      summary: {
        count: `${count} entries recorded`,
        location: location,
        submittedAt: newDailyCount.submittedAt,
      },
    });
  } catch (error) {
    console.error("Create daily count error:", error);
    res.status(500).json({
      error: "Failed to submit daily count",
      message: "An error occurred while submitting the daily count",
    });
  }
});

// PUT /api/daily-counts/:id - Update daily count
router.put("/:id", dailyCountValidation, async (req, res) => {
  try {
    const { id } = req.params;
    const countIndex = dailyCounts.findIndex((dc) => dc.id === id);

    if (countIndex === -1) {
      return res.status(404).json({
        error: "Daily count not found",
        message: `No daily count found with ID: ${id}`,
      });
    }

    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
        autoFix: "Some fields have been automatically corrected",
      });
    }

    const {
      employeeId,
      fieldEmployeeId,
      date,
      institute,
      product,
      location,
      count,
      notes = "",
    } = req.body;

    // Update the daily count entry
    dailyCounts[countIndex] = {
      ...dailyCounts[countIndex],
      employeeId,
      fieldEmployeeId,
      date,
      institute,
      product,
      location,
      count: parseInt(count),
      notes,
      updatedAt: new Date().toISOString(),
    };

    res.json({
      message: "Daily count updated successfully",
      dailyCount: dailyCounts[countIndex],
    });
  } catch (error) {
    console.error("Update daily count error:", error);
    res.status(500).json({
      error: "Failed to update daily count",
      message: "An error occurred while updating the daily count",
    });
  }
});

// DELETE /api/daily-counts/:id - Delete daily count
router.delete("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const countIndex = dailyCounts.findIndex((dc) => dc.id === id);

    if (countIndex === -1) {
      return res.status(404).json({
        error: "Daily count not found",
        message: `No daily count found with ID: ${id}`,
      });
    }

    const deletedCount = dailyCounts.splice(countIndex, 1)[0];

    res.json({
      message: "Daily count deleted successfully",
      deletedCount,
    });
  } catch (error) {
    console.error("Delete daily count error:", error);
    res.status(500).json({
      error: "Failed to delete daily count",
      message: "An error occurred while deleting the daily count",
    });
  }
});

// GET /api/daily-counts/reports/summary - Generate summary report
router.get("/reports/summary", async (req, res) => {
  try {
    const {
      dateFrom = "",
      dateTo = "",
      employeeId = "",
      location = "",
      institute = "",
      product = "",
    } = req.query;

    let reportData = [...dailyCounts];

    // Apply date filters
    if (dateFrom && dateTo) {
      reportData = reportData.filter(
        (dc) => dc.date >= dateFrom && dc.date <= dateTo
      );
    }

    if (employeeId) {
      reportData = reportData.filter((dc) => dc.employeeId === employeeId);
    }

    if (location) {
      reportData = reportData.filter((dc) => dc.location === location);
    }

    if (institute) {
      reportData = reportData.filter((dc) => dc.institute === institute);
    }

    if (product) {
      reportData = reportData.filter((dc) => dc.product === product);
    }

    // Generate summary with auto-error recovery
    const summary = {
      period: { from: dateFrom || "All time", to: dateTo || "All time" },
      totalRecords: reportData.length,
      totalCount: 0,
      averageCountPerDay: 0,
      byEmployee: {},
      byLocation: {},
      byInstitute: {},
      byProduct: {},
      dailyBreakdown: {},
    };

    try {
      reportData.forEach((dc) => {
        summary.totalCount += dc.count;

        // By employee
        if (!summary.byEmployee[dc.employeeId]) {
          summary.byEmployee[dc.employeeId] = {
            name: dc.employeeName,
            count: 0,
            days: 0,
          };
        }
        summary.byEmployee[dc.employeeId].count += dc.count;
        summary.byEmployee[dc.employeeId].days += 1;

        // By location
        summary.byLocation[dc.location] =
          (summary.byLocation[dc.location] || 0) + dc.count;

        // By institute
        summary.byInstitute[dc.institute] =
          (summary.byInstitute[dc.institute] || 0) + dc.count;

        // By product
        summary.byProduct[dc.product] =
          (summary.byProduct[dc.product] || 0) + dc.count;

        // Daily breakdown
        summary.dailyBreakdown[dc.date] =
          (summary.dailyBreakdown[dc.date] || 0) + dc.count;
      });

      // Calculate averages
      const uniqueDays = new Set(reportData.map((dc) => dc.date)).size;
      summary.averageCountPerDay =
        uniqueDays > 0
          ? Math.round((summary.totalCount / uniqueDays) * 100) / 100
          : 0;
    } catch (calcError) {
      console.warn("Report calculation error:", calcError);
      summary.error = "Some calculations failed";
    }

    res.json({
      report: summary,
      generatedAt: new Date().toISOString(),
      filters: { dateFrom, dateTo, employeeId, location, institute, product },
    });
  } catch (error) {
    console.error("Generate report error:", error);
    res.status(500).json({
      error: "Failed to generate report",
      message: "An error occurred while generating the summary report",
    });
  }
});

module.exports = router;
