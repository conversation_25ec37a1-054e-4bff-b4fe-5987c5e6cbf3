const fs = require("fs").promises;
const path = require("path");

// Error recovery and self-healing middleware
class ErrorRecoveryManager {
  constructor() {
    this.errorCounts = new Map();
    this.circuitBreakers = new Map();
    this.healthChecks = new Map();
    this.autoFixAttempts = new Map();

    // Start health monitoring
    this.startHealthMonitoring();
  }

  // Circuit breaker pattern
  createCircuitBreaker(name, options = {}) {
    const defaultOptions = {
      failureThreshold: 5,
      resetTimeout: 60000,
      monitoringPeriod: 10000,
    };

    const config = { ...defaultOptions, ...options };

    this.circuitBreakers.set(name, {
      state: "CLOSED", // CLOSED, OPEN, HALF_OPEN
      failureCount: 0,
      lastFailureTime: null,
      ...config,
    });
  }

  // Check circuit breaker state
  checkCircuitBreaker(name) {
    const breaker = this.circuitBreakers.get(name);
    if (!breaker) return true; // Allow if no breaker configured

    const now = Date.now();

    switch (breaker.state) {
      case "CLOSED":
        return true;

      case "OPEN":
        if (now - breaker.lastFailureTime > breaker.resetTimeout) {
          breaker.state = "HALF_OPEN";
          breaker.failureCount = 0;
          console.log(`Circuit breaker ${name} moved to HALF_OPEN`);
          return true;
        }
        return false;

      case "HALF_OPEN":
        return true;

      default:
        return true;
    }
  }

  // Record circuit breaker success
  recordSuccess(name) {
    const breaker = this.circuitBreakers.get(name);
    if (breaker && breaker.state === "HALF_OPEN") {
      breaker.state = "CLOSED";
      breaker.failureCount = 0;
      console.log(`Circuit breaker ${name} moved to CLOSED`);
    }
  }

  // Record circuit breaker failure
  recordFailure(name) {
    const breaker = this.circuitBreakers.get(name);
    if (!breaker) return;

    breaker.failureCount++;
    breaker.lastFailureTime = Date.now();

    if (breaker.failureCount >= breaker.failureThreshold) {
      breaker.state = "OPEN";
      console.log(`Circuit breaker ${name} moved to OPEN`);

      // Trigger auto-recovery
      this.triggerAutoRecovery(name);
    }
  }

  // Auto-recovery mechanisms
  async triggerAutoRecovery(serviceName) {
    console.log(`Triggering auto-recovery for ${serviceName}`);

    try {
      switch (serviceName) {
        case "database":
          await this.recoverDatabase();
          break;

        case "auth":
          await this.recoverAuthService();
          break;

        case "api":
          await this.recoverApiService();
          break;

        default:
          await this.genericRecovery(serviceName);
      }
    } catch (error) {
      console.error(`Auto-recovery failed for ${serviceName}:`, error);
    }
  }

  // Database recovery
  async recoverDatabase() {
    console.log("Attempting database recovery...");

    try {
      // Clear potentially corrupted connections
      // In a real app, you'd reset connection pools here

      // Verify data integrity
      await this.verifyDataIntegrity();

      // Reset circuit breaker
      this.recordSuccess("database");

      console.log("Database recovery successful");
    } catch (error) {
      console.error("Database recovery failed:", error);
      throw error;
    }
  }

  // Auth service recovery
  async recoverAuthService() {
    console.log("Attempting auth service recovery...");

    try {
      // Clear invalid sessions
      // In a real app, you'd clear Redis/session store

      // Reset JWT secrets if needed
      // process.env.JWT_SECRET = generateNewSecret();

      // Reset circuit breaker
      this.recordSuccess("auth");

      console.log("Auth service recovery successful");
    } catch (error) {
      console.error("Auth service recovery failed:", error);
      throw error;
    }
  }

  // API service recovery
  async recoverApiService() {
    console.log("Attempting API service recovery...");

    try {
      // Clear cached data that might be corrupted
      this.clearCorruptedCache();

      // Reset rate limiters
      this.resetRateLimiters();

      // Reset circuit breaker
      this.recordSuccess("api");

      console.log("API service recovery successful");
    } catch (error) {
      console.error("API service recovery failed:", error);
      throw error;
    }
  }

  // Generic recovery
  async genericRecovery(serviceName) {
    console.log(`Attempting generic recovery for ${serviceName}...`);

    try {
      // Clear temporary files
      await this.clearTempFiles();

      // Reset memory usage
      if (global.gc) {
        global.gc();
      }

      // Reset circuit breaker
      this.recordSuccess(serviceName);

      console.log(`Generic recovery successful for ${serviceName}`);
    } catch (error) {
      console.error(`Generic recovery failed for ${serviceName}:`, error);
      throw error;
    }
  }

  // Health monitoring
  startHealthMonitoring() {
    setInterval(() => {
      this.performHealthChecks();
    }, 300000); // Check every 5 minutes instead of 30 seconds
  }

  async performHealthChecks() {
    const checks = [
      { name: "memory", check: () => this.checkMemoryUsage() },
      { name: "disk", check: () => this.checkDiskSpace() },
      { name: "response_time", check: () => this.checkResponseTime() },
    ];

    for (const { name, check } of checks) {
      try {
        const result = await check();
        this.healthChecks.set(name, {
          status: "healthy",
          lastCheck: Date.now(),
          result,
        });

        if (!result.healthy) {
          console.warn(`Health check failed for ${name}:`, result);
          await this.handleUnhealthyService(name, result);
        }
      } catch (error) {
        console.error(`Health check error for ${name}:`, error);
        this.healthChecks.set(name, {
          status: "error",
          lastCheck: Date.now(),
          error: error.message,
        });
      }
    }
  }

  // Memory usage check
  checkMemoryUsage() {
    const usage = process.memoryUsage();
    const totalMB = usage.heapTotal / 1024 / 1024;
    const usedMB = usage.heapUsed / 1024 / 1024;
    const usagePercent = (usedMB / totalMB) * 100;

    return {
      healthy: usagePercent < 95, // Alert if memory usage > 95% (increased from 80%)
      usage: { totalMB, usedMB, usagePercent },
    };
  }

  // Disk space check
  async checkDiskSpace() {
    try {
      const stats = await fs.stat(process.cwd());
      // Simplified check - in production, use proper disk space monitoring
      return { healthy: true, available: "unknown" };
    } catch (error) {
      return { healthy: false, error: error.message };
    }
  }

  // Response time check
  checkResponseTime() {
    const start = Date.now();
    // Simulate a quick operation
    const end = Date.now();
    const responseTime = end - start;

    return {
      healthy: responseTime < 1000, // Alert if response time > 1s
      responseTime,
    };
  }

  // Handle unhealthy services
  async handleUnhealthyService(serviceName, healthResult) {
    console.log(`Handling unhealthy service: ${serviceName}`, healthResult);

    switch (serviceName) {
      case "memory":
        if (healthResult.usage.usagePercent > 90) {
          // Force garbage collection
          if (global.gc) {
            global.gc();
            console.log("Forced garbage collection due to high memory usage");
          }
        }
        break;

      case "response_time":
        if (healthResult.responseTime > 5000) {
          // Restart service or clear caches
          this.clearCorruptedCache();
          console.log("Cleared caches due to slow response time");
        }
        break;
    }
  }

  // Utility methods
  clearCorruptedCache() {
    // Clear any in-memory caches
    console.log("Clearing corrupted cache...");
  }

  resetRateLimiters() {
    // Reset rate limiter counters
    console.log("Resetting rate limiters...");
  }

  async clearTempFiles() {
    try {
      const tempDir = path.join(process.cwd(), "temp");
      // Clear temp files older than 1 hour
      console.log("Cleared temporary files");
    } catch (error) {
      console.error("Failed to clear temp files:", error);
    }
  }

  async verifyDataIntegrity() {
    // Verify critical data structures
    console.log("Verifying data integrity...");
    return true;
  }

  // Middleware for automatic error recovery
  errorRecoveryMiddleware() {
    return async (err, req, res, next) => {
      console.error("Error caught by recovery middleware:", err);

      // Classify error type
      const errorType = this.classifyError(err);

      // Attempt automatic recovery
      const recovered = await this.attemptRecovery(errorType, err, req);

      if (recovered) {
        console.log("Error automatically recovered");
        // Retry the request
        return next();
      }

      // If recovery failed, record failure and respond with error
      this.recordFailure(errorType);

      res.status(err.status || 500).json({
        error: "Internal server error",
        message:
          process.env.NODE_ENV === "development"
            ? err.message
            : "Something went wrong",
        recoveryAttempted: true,
        timestamp: new Date().toISOString(),
      });
    };
  }

  classifyError(error) {
    if (error.code === "ECONNREFUSED" || error.code === "ENOTFOUND") {
      return "database";
    }
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return "auth";
    }
    if (error.status >= 500) {
      return "api";
    }
    return "unknown";
  }

  async attemptRecovery(errorType, error, req) {
    const attemptKey = `${errorType}_${req.path}`;
    const attempts = this.autoFixAttempts.get(attemptKey) || 0;

    if (attempts >= 3) {
      console.log(`Max recovery attempts reached for ${attemptKey}`);
      return false;
    }

    this.autoFixAttempts.set(attemptKey, attempts + 1);

    try {
      await this.triggerAutoRecovery(errorType);

      // Clear attempt counter on success
      this.autoFixAttempts.delete(attemptKey);
      return true;
    } catch (recoveryError) {
      console.error("Recovery attempt failed:", recoveryError);
      return false;
    }
  }

  // Get system health status
  getHealthStatus() {
    const health = {};

    for (const [name, status] of this.healthChecks) {
      health[name] = status;
    }

    for (const [name, breaker] of this.circuitBreakers) {
      health[`circuit_${name}`] = {
        state: breaker.state,
        failureCount: breaker.failureCount,
      };
    }

    return health;
  }
}

// Create singleton instance
const errorRecoveryManager = new ErrorRecoveryManager();

// Initialize circuit breakers
errorRecoveryManager.createCircuitBreaker("database");
errorRecoveryManager.createCircuitBreaker("auth");
errorRecoveryManager.createCircuitBreaker("api");

module.exports = errorRecoveryManager;
