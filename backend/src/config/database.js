const { Pool } = require('pg');
require('dotenv').config();

// Database configuration with connection pooling for better performance
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'acsbilling',
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,
  // Performance optimizations
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
  maxUses: 7500, // Close (and replace) a connection after it has been used 7500 times
});

// Enhanced error handling
pool.on('error', (err, client) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

// Connection health check
pool.on('connect', (client) => {
  console.log('New client connected to PostgreSQL');
});

pool.on('remove', (client) => {
  console.log('Client removed from PostgreSQL pool');
});

// Database query wrapper with automatic retry and performance monitoring
const query = async (text, params) => {
  const start = Date.now();
  const maxRetries = 3;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      const res = await pool.query(text, params);
      const duration = Date.now() - start;
      
      // Log slow queries (> 1000ms)
      if (duration > 1000) {
        console.warn(`Slow query detected: ${duration}ms - ${text.substring(0, 100)}...`);
      }
      
      return res;
    } catch (error) {
      retries++;
      console.error(`Database query error (attempt ${retries}/${maxRetries}):`, error.message);
      
      if (retries === maxRetries) {
        throw error;
      }
      
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retries - 1)));
    }
  }
};

// Transaction wrapper with automatic rollback
const transaction = async (callback) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};

// Database health check
const healthCheck = async () => {
  try {
    const result = await query('SELECT NOW() as current_time');
    return {
      status: 'healthy',
      timestamp: result.rows[0].current_time,
      pool_total: pool.totalCount,
      pool_idle: pool.idleCount,
      pool_waiting: pool.waitingCount
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      pool_total: pool.totalCount,
      pool_idle: pool.idleCount,
      pool_waiting: pool.waitingCount
    };
  }
};

// Graceful shutdown
const closePool = async () => {
  try {
    await pool.end();
    console.log('PostgreSQL pool has ended');
  } catch (error) {
    console.error('Error closing PostgreSQL pool:', error);
  }
};

// Handle process termination
process.on('SIGINT', closePool);
process.on('SIGTERM', closePool);

module.exports = {
  pool,
  query,
  transaction,
  healthCheck,
  closePool
};
