const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const compression = require("compression");
require("dotenv").config();

// Import security middleware
const {
  generalLimiter,
  securityHeaders,
  sanitizeInput,
  securityLogger,
} = require("./middleware/security");

// Import error recovery
const errorRecoveryManager = require("./middleware/errorRecovery");

// Import routes
const { router: authRoutes } = require("./routes/auth");
const employeeRoutes = require("./routes/employees");
const dailyCountRoutes = require("./routes/dailyCounts");
const mastersRoutes = require("./routes/masters");
const dashboardRoutes = require("./routes/dashboard");

const app = express();

// Security middleware
app.use(securityLogger);
app.use(securityHeaders);
app.use(
  helmet({
    contentSecurityPolicy: false, // We set this manually in securityHeaders
    crossOriginEmbedderPolicy: false,
  })
);

// Compression middleware for better performance
app.use(
  compression({
    level: 6, // Compression level (1-9, 6 is default)
    threshold: 1024, // Only compress responses larger than 1KB
    filter: (req, res) => {
      // Don't compress if the request includes a cache-control no-transform directive
      if (
        req.headers["cache-control"] &&
        req.headers["cache-control"].includes("no-transform")
      ) {
        return false;
      }
      // Use compression filter function
      return compression.filter(req, res);
    },
  })
);

app.use(
  cors({
    origin: [
      "http://localhost:5173",
      "http://localhost:5174",
      process.env.FRONTEND_URL,
    ].filter(Boolean),
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
    maxAge: 86400, // 24 hours
  })
);

// Rate limiting
app.use(generalLimiter);

// Input sanitization
app.use(sanitizeInput);

// Logging
app.use(morgan("combined"));

// Body parsing
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint with system status
app.get("/health", (req, res) => {
  const healthStatus = errorRecoveryManager.getHealthStatus();
  res.status(200).json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    health: healthStatus,
  });
});

// API routes
app.use("/api/auth", authRoutes);
app.use("/api/employees", employeeRoutes);
app.use("/api/daily-counts", dailyCountRoutes);
app.use("/api/masters", mastersRoutes);
app.use("/api/dashboard", dashboardRoutes);

// Error recovery middleware (must be before general error handler)
app.use(errorRecoveryManager.errorRecoveryMiddleware());

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: "Something went wrong!",
    error: process.env.NODE_ENV === "development" ? err.message : undefined,
    timestamp: new Date().toISOString(),
  });
});

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({ message: "Route not found" });
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;
