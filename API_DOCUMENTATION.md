# 🔌 Employee Billing System - API Documentation

## 📋 Table of Contents
1. [Base URL & Authentication](#base-url--authentication)
2. [Employee Management APIs](#employee-management-apis)
3. [Daily Count APIs](#daily-count-apis)
4. [Master Data APIs](#master-data-apis)
5. [Dashboard APIs](#dashboard-apis)
6. [Response Formats](#response-formats)
7. [Erro<PERSON> Handling](#error-handling)
8. [Testing Examples](#testing-examples)

## 🌐 Base URL & Authentication

### **Base URL**
```
http://localhost:3000/api
```

### **Headers**
```json
{
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

### **Authentication**
Currently using basic authentication. JWT implementation planned for future releases.

## 👥 Employee Management APIs

### **List All Employees**
```http
GET /api/employees
```

**Response:**
```json
{
  "employees": [
    {
      "id": "uuid",
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "role": "developer",
      "team": "Development Team",
      "status": "active",
      "created_at": "2025-05-28T10:00:00Z"
    }
  ],
  "total": 2,
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}
```

### **Create New Employee**
```http
POST /api/employees
```

**Request Body:**
```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "phone": "+1234567891",
  "roleId": "role-uuid",
  "teamId": "team-uuid"
}
```

**Response:**
```json
{
  "message": "Employee created successfully",
  "employee": {
    "id": "new-uuid",
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "status": "active"
  }
}
```

### **Update Employee**
```http
PUT /api/employees/:id
```

### **Delete Employee**
```http
DELETE /api/employees/:id
```

### **Employee Roles**
```http
GET /api/employees/roles
POST /api/employees/roles
```

**Create Role Request:**
```json
{
  "name": "manager",
  "description": "Team manager role"
}
```

### **Employee Teams**
```http
GET /api/employees/teams
POST /api/employees/teams
```

**Create Team Request:**
```json
{
  "name": "QA Team",
  "description": "Quality Assurance team"
}
```

## 📊 Daily Count APIs

### **List Daily Counts**
```http
GET /api/daily-counts
```

**Query Parameters:**
- `employeeId` - Filter by employee
- `date` - Filter by specific date
- `dateFrom` - Start date range
- `dateTo` - End date range
- `status` - Filter by status (pending/approved/rejected)
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10)

**Response:**
```json
{
  "dailyCounts": [
    {
      "id": "1",
      "employeeId": "uuid",
      "employeeName": "John Doe",
      "date": "2025-05-28",
      "institute": "Test Institute",
      "product": "Background Verification",
      "location": "local",
      "count": 8,
      "rate": 500,
      "totalAmount": 4000,
      "status": "pending",
      "submittedAt": "2025-05-28T13:29:10Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 1,
    "totalItems": 1,
    "itemsPerPage": 10
  },
  "summary": {
    "totalRecords": 1,
    "totalHours": 8,
    "totalAmount": 4000,
    "byStatus": {
      "pending": 1,
      "approved": 0,
      "rejected": 0
    }
  }
}
```

### **Submit Daily Count**
```http
POST /api/daily-counts
```

**Request Body:**
```json
{
  "employeeId": "uuid",
  "date": "2025-05-29",
  "institute": "Test Institute",
  "product": "Background Verification",
  "location": "local",
  "count": 7,
  "rate": 500
}
```

**Response:**
```json
{
  "message": "Daily count submitted successfully",
  "dailyCount": {
    "id": "2",
    "employeeId": "uuid",
    "date": "2025-05-29",
    "totalAmount": 3500,
    "status": "pending"
  },
  "autoCalculations": {
    "totalAmount": "Calculated as 7 × 500 = 3500",
    "status": "Set to pending for approval"
  }
}
```

### **Approve Daily Count**
```http
PUT /api/daily-counts/:id/approve
```

### **Reject Daily Count**
```http
PUT /api/daily-counts/:id/reject
```

**Request Body:**
```json
{
  "reason": "Incorrect count reported"
}
```

### **Daily Count Reports**
```http
GET /api/daily-counts/reports/summary
```

## 🏛️ Master Data APIs

### **Institutes**
```http
GET /api/masters/institutes
POST /api/masters/institutes
PUT /api/masters/institutes/:id
DELETE /api/masters/institutes/:id
```

**Create Institute:**
```json
{
  "name": "ABC University",
  "code": "ABC001",
  "description": "Leading educational institution"
}
```

### **Products**
```http
GET /api/masters/products
POST /api/masters/products
PUT /api/masters/products/:id
DELETE /api/masters/products/:id
```

**Create Product:**
```json
{
  "name": "Background Verification",
  "description": "Employee background verification service"
}
```

### **Services**
```http
GET /api/masters/services
POST /api/masters/services
PUT /api/masters/services/:id
DELETE /api/masters/services/:id
```

### **Locations**
```http
GET /api/masters/locations
POST /api/masters/locations
PUT /api/masters/locations/:id
DELETE /api/masters/locations/:id
```

**Create Location:**
```json
{
  "name": "Mumbai Office",
  "type": "local"
}
```

### **Rates**
```http
GET /api/masters/rates
POST /api/masters/rates
PUT /api/masters/rates/:id
DELETE /api/masters/rates/:id
```

**Create Rate:**
```json
{
  "serviceType": "Background Verification",
  "rate": 500.00,
  "effectiveFrom": "2025-01-01",
  "effectiveTo": "2025-12-31"
}
```

## 📈 Dashboard APIs

### **Dashboard Statistics**
```http
GET /api/dashboard/stats
```

**Response:**
```json
{
  "totalEmployees": 2,
  "totalDailyCounts": 2,
  "totalRoles": 4,
  "totalTeams": 3,
  "pendingApprovals": 2,
  "monthlyRevenue": 7500,
  "averageDaily": 7.5
}
```

### **Recent Activity**
```http
GET /api/dashboard/recent-activity
```

### **System Health**
```http
GET /api/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-05-29T06:30:00Z",
  "services": {
    "database": {
      "status": "connected",
      "responseTime": "5ms"
    },
    "memory": {
      "status": "healthy",
      "usage": "85%"
    }
  }
}
```

## 📝 Response Formats

### **Success Response**
```json
{
  "message": "Operation successful",
  "data": { /* response data */ },
  "timestamp": "2025-05-29T06:30:00Z"
}
```

### **Error Response**
```json
{
  "error": "Validation failed",
  "message": "Employee name is required",
  "details": {
    "field": "name",
    "code": "REQUIRED_FIELD"
  },
  "timestamp": "2025-05-29T06:30:00Z"
}
```

### **Pagination Format**
```json
{
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 50,
    "itemsPerPage": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## ⚠️ Error Handling

### **HTTP Status Codes**
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict (Duplicate entry)
- `422` - Validation Error
- `500` - Internal Server Error

### **Common Error Scenarios**

#### **Duplicate Entry**
```json
{
  "error": "Duplicate entry",
  "message": "Daily count for employee on 2025-05-28 already exists",
  "suggestion": "Update the existing entry instead",
  "existingEntry": { /* existing record */ }
}
```

#### **Validation Error**
```json
{
  "error": "Validation failed",
  "message": "Please fill in all required fields",
  "details": {
    "missingFields": ["employeeId", "institute", "product"]
  }
}
```

## 🧪 Testing Examples

### **cURL Examples**

#### **Create Employee**
```bash
curl -X POST http://localhost:3000/api/employees \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "phone": "+1234567890"
  }'
```

#### **Submit Daily Count**
```bash
curl -X POST http://localhost:3000/api/daily-counts \
  -H "Content-Type: application/json" \
  -d '{
    "employeeId": "uuid",
    "date": "2025-05-29",
    "institute": "Test Institute",
    "product": "Background Verification",
    "location": "local",
    "count": 8,
    "rate": 500
  }'
```

#### **Get Dashboard Stats**
```bash
curl -X GET http://localhost:3000/api/dashboard/stats
```

### **JavaScript Examples**

#### **Fetch Daily Counts**
```javascript
const response = await fetch('/api/daily-counts?status=pending');
const data = await response.json();
console.log(data.dailyCounts);
```

#### **Submit Daily Count**
```javascript
const response = await fetch('/api/daily-counts', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    employeeId: 'uuid',
    date: '2025-05-29',
    institute: 'Test Institute',
    product: 'Background Verification',
    count: 8,
    rate: 500
  })
});

const result = await response.json();
```

---

## 📞 Support

For API support or questions:
- Check the main documentation: `COMPLETE_IMPLEMENTATION_DOCUMENTATION.md`
- Review error responses for detailed information
- Test endpoints using the provided examples

**API Version**: 1.0.0
**Last Updated**: May 29, 2025
