{"totalCounts": 14, "todayCounts": 0, "instituteCounts": [{"institute_id": "1", "institute_name": "Institute A", "count": 8}, {"institute_id": "2", "institute_name": "Institute B", "count": 6}], "productCounts": [{"product_id": "1", "product_name": "Product A", "count": 7}, {"product_id": "2", "product_name": "Product B", "count": 7}], "instituteProductCounts": [{"key": "1-1", "institute_id": "1", "institute_name": "Institute A", "product_id": "1", "product_name": "Product A", "count": 5, "services": [{"service_id": "1", "service_name": "Service A", "count": 5, "location_type": "local", "verifier_name": "<PERSON>"}]}, {"key": "1-2", "institute_id": "1", "institute_name": "Institute A", "product_id": "2", "product_name": "Product B", "count": 3, "services": [{"service_id": "2", "service_name": "Service B", "count": 3, "location_type": "ogl", "verifier_name": "<PERSON>"}]}, {"key": "2-1", "institute_id": "2", "institute_name": "Institute B", "product_id": "1", "product_name": "Product A", "count": 2, "services": [{"service_id": "1", "service_name": "Service A", "count": 2, "location_type": "outstation", "verifier_name": "<PERSON>"}]}, {"key": "2-2", "institute_id": "2", "institute_name": "Institute B", "product_id": "2", "product_name": "Product B", "count": 4, "services": [{"service_id": "2", "service_name": "Service B", "count": 4, "location_type": "local", "verifier_name": "Alice <PERSON>"}]}], "teamCounts": [{"team_id": "1", "team_name": "Team 1", "count": 10}, {"team_id": "2", "team_name": "Team 2", "count": 4}], "locationCounts": {"local": 9, "ogl": 3, "outstation": 2}}